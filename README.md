# Kylas Customer Success Portal

A comprehensive customer success management platform built with Ruby on Rails for managing Kylas customer accounts, tracking usage analytics, handling customer requirements, and providing support services.

## 📋 Table of Contents

- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Database Schema](#database-schema)
- [Application Flow](#application-flow)
- [User Management](#user-management)
- [Features](#features)
- [Technology Stack](#technology-stack)
- [Setup Instructions](#setup-instructions)
- [API Integrations](#api-integrations)
- [Deployment](#deployment)
- [Testing](#testing)

## 🎯 Overview

The Kylas Customer Success Portal is designed to help customer success teams manage and track customer accounts, analyze usage patterns, handle customer requirements, and provide comprehensive support. The application integrates with various external services including AWS S3, Chargebee, and the Kylas marketplace API.

### Key Capabilities

- **Account Management**: Comprehensive customer account tracking and management
- **Usage Analytics**: Real-time usage data synchronization and analysis
- **Customer Requirements**: Track and manage customer feature requests and requirements
- **RGS (Revenue Growth Strategy) Inputs**: Collect and manage customer growth data
- **User Management**: Role-based access control with admin and user roles
- **Data Synchronization**: Automated data sync from external sources

## 🏗️ System Architecture

The application follows a layered architecture pattern with clear separation of concerns:

### Architecture Components

- **Web Layer**: Rails controllers and views handling HTTP requests
- **Service Layer**: Business logic encapsulated in service objects
- **Data Layer**: ActiveRecord models and PostgreSQL database
- **External Integrations**: AWS S3, Chargebee, and Kylas Marketplace APIs
- **Background Processing**: Cron jobs for data synchronization
- **Authentication**: Devise-based user management with role-based access

### Data Flow

1. **External Data Sync**: Daily cron jobs fetch data from AWS S3 (onboarding and usage data)
2. **User Interaction**: Web interface allows CRUD operations on customer data
3. **Real-time Updates**: Service layer handles business logic and external API calls
4. **Data Persistence**: All changes are persisted to PostgreSQL database
5. **Monitoring**: Comprehensive logging and error tracking across all layers

## 🗄️ Database Schema

The application uses PostgreSQL with the following core entities:

### Core Models

- **Tenants**: Central entity representing Kylas customer organizations
- **Users**: Application users with role-based access (Admin/User)
- **AccountDetails**: Comprehensive customer account information and metrics
- **CustomerAsks**: Customer feature requests and requirements management
- **RgsInputs**: Revenue Growth Strategy data collection
- **Usages**: Real-time customer usage analytics

### Key Relationships

- Each Tenant has one AccountDetail and can have multiple Usage records
- Users can be assigned as account managers or support executives
- CustomerAsks contain multiple CustomerRequirements
- RgsInputs contain multiple Entities and MarketplaceApps
- Historical data is maintained for AccountDetails and Usage tracking

## 🔄 Application Flow

The application provides different user experiences based on roles and authentication status:

### Authentication Flow
1. **Login Process**: Users authenticate via Devise with email/password
2. **Role-Based Access**: System redirects based on user role (Admin/User)
3. **Session Management**: Secure session handling with "Remember Me" functionality

### Main User Journeys

#### Admin Users
- **User Management**: Invite new users, deactivate accounts, resend invitations
- **Full Account Access**: View and manage all customer accounts
- **System Administration**: Access to all features and data

#### Regular Users
- **Account Viewing**: Access to customer account information
- **Data Updates**: Update account details, RGS inputs, and customer requirements
- **Limited Permissions**: Cannot manage other users

### Account Management Workflow
1. **Account Selection**: Browse paginated list of customer accounts
2. **Detailed View**: Access comprehensive account information via tabs
3. **Data Updates**: Modify account details, plan information, and requirements
4. **External Integration**: Real-time data sync with Chargebee and marketplace APIs

## 👥 User Management

The application implements a comprehensive user management system with role-based access control:

### User Roles

#### Admin Users
- **Full System Access**: Complete access to all features and data
- **User Management**: Can invite, activate, and deactivate users
- **Account Management**: Full CRUD operations on all customer accounts
- **System Administration**: Access to reports and system-wide settings

#### Regular Users
- **Limited Access**: Can view and update customer account information
- **No User Management**: Cannot invite or manage other users
- **Account Operations**: Can update account details, RGS inputs, and customer requirements

### Authentication Features

- **Devise Integration**: Secure authentication with email/password
- **Invitation System**: Admin-only user invitation workflow
- **Session Management**: Secure session handling with optional "Remember Me"
- **Password Security**: Strong password requirements and reset functionality
- **Account Status**: Users can be deactivated without deletion

### Security Measures

- **Request Logging**: Comprehensive logging of all requests with user context
- **IP Tracking**: Request IP and remote IP logging for security auditing
- **Role Validation**: Strict role-based access control on all admin features
- **Session Security**: Secure session management with proper timeout handling

## ✨ Features

### Core Functionality

#### Account Management
- **Customer Account Tracking**: Comprehensive customer information management
- **Account Details**: Contact information, industry, company details
- **Account Metrics**: User counts, activity tracking, marketplace app usage
- **Account History**: Historical tracking of all account changes
- **Account Assignment**: Assign account managers and support executives

#### Usage Analytics
- **Real-time Usage Data**: Daily usage statistics and metrics
- **User Activity Tracking**: Login patterns, feature usage, DAU/MAU metrics
- **Performance Metrics**: Lead/deal/contact creation and update tracking
- **Historical Analytics**: Usage history and trend analysis
- **Custom Dashboards**: User-created dashboard tracking

#### Customer Requirements Management
- **Requirement Tracking**: Manage customer feature requests and requirements
- **Status Management**: Track requirement status (Pending, In Progress, Completed, etc.)
- **Category Classification**: Organize requirements by category (Lead, Deal, Contact, etc.)
- **Requirement History**: Complete audit trail of requirement changes
- **Bulk Operations**: Add, edit, and delete multiple requirements

#### RGS (Revenue Growth Strategy) Inputs
- **Growth Data Collection**: Capture customer growth strategy information
- **Entity Management**: Track critical business entities and volumes
- **Marketplace Integration**: Monitor marketplace app installations and usage
- **Frequency Tracking**: Monitor data update frequencies and patterns
- **Strategic Planning**: Support revenue growth planning and analysis

### Advanced Features

#### Data Synchronization
- **AWS S3 Integration**: Automated daily data sync from S3 buckets
- **Chargebee Integration**: Real-time billing and subscription data
- **Marketplace API**: Live marketplace app data synchronization
- **Background Processing**: Automated cron jobs for data updates
- **Error Handling**: Robust error handling and retry mechanisms

#### Reporting & Analytics
- **Usage Reports**: Comprehensive usage analytics and reporting
- **Account Reports**: Detailed account performance and status reports
- **Trend Analysis**: Historical data analysis and trend identification
- **Export Capabilities**: Data export functionality for external analysis
- **Real-time Dashboards**: Live data visualization and monitoring

## 🛠️ Technology Stack

### Backend Framework
- **Ruby 3.1.0**: Modern Ruby version with performance improvements
- **Rails 7.0.4+**: Latest Rails framework with enhanced features
- **PostgreSQL**: Robust relational database for data persistence

### Authentication & Authorization
- **Devise**: Comprehensive authentication solution
- **Devise Invitable**: User invitation system
- **Role-Based Access Control**: Custom admin/user role implementation

### Frontend Technologies
- **ERB Templates**: Server-side rendered views
- **Bootstrap 5.0.2**: Responsive UI framework
- **SCSS**: Enhanced CSS with variables and mixins
- **JavaScript**: Client-side interactivity and form handling

### External Integrations
- **AWS SDK S3**: Cloud storage integration for data synchronization
- **Chargebee API**: Billing and subscription management
- **Kylas Marketplace API**: Marketplace app data integration
- **Net::HTTP**: HTTP client for external API communications

### Development & Testing
- **RSpec**: Behavior-driven testing framework
- **Factory Bot**: Test data generation
- **Faker**: Realistic fake data for testing
- **SimpleCov**: Code coverage analysis
- **WebMock**: HTTP request stubbing for tests

### Background Processing & Scheduling
- **Whenever**: Cron job management
- **ActiveJob**: Background job processing framework
- **Cron**: Scheduled task execution

### Monitoring & Logging
- **Honeybadger**: Error tracking and monitoring
- **LogDNA**: Centralized log management
- **Lograge**: Structured request logging
- **Silencer**: Log filtering and noise reduction

### Development Tools
- **Puma**: High-performance web server
- **Bootsnap**: Application boot time optimization
- **Pagy**: Efficient pagination
- **Cocoon**: Dynamic nested forms
- **Momentjs**: Date and time manipulation

### Deployment & Infrastructure
- **Mina**: Fast deployment tool
- **Capistrano-style**: Deployment automation
- **Production Environment**: Optimized for production deployment

## 🗄️ Database Configuration

### Database Requirements

The application uses **PostgreSQL** as its primary database with the following requirements:

- **PostgreSQL 9.3+** (PostgreSQL 15 recommended)
- **Databases**:
  - `kylas_customer_success_development` (Development)
  - `kylas_customer_success_test` (Testing)
  - `kylas_customer_success_staging` (Staging)
  - `kylas_customer_success_production` (Production)

### Database Setup Options

#### Option 1: Docker Setup (Recommended)

The easiest way to set up the database is using Docker Compose:

```bash
# Start PostgreSQL containers
docker-compose up -d postgres

# Verify containers are running
docker-compose ps

# Run database setup
bin/setup_database
```

**Benefits:**
- ✅ No local PostgreSQL installation required
- ✅ Consistent environment across team members
- ✅ Automatic database creation
- ✅ Separate test database container
- ✅ Easy cleanup and reset

#### Option 2: Local PostgreSQL Installation

If you prefer a local PostgreSQL installation:

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql
brew services start postgresql

# Create PostgreSQL user (if needed)
sudo -u postgres createuser -s postgres
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'password';"

# Run database setup
bin/setup_database
```

#### Option 3: Manual Database Setup

For manual control over the database setup:

```bash
# Create databases manually
createdb kylas_customer_success_development
createdb kylas_customer_success_test

# Run migrations
rails db:migrate
RAILS_ENV=test rails db:migrate

# Load seed data
rails db:seed
```

### Database Configuration

The database configuration is located in `config/database.yml`:

```yaml
default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  username: postgres
  password: password
  host: localhost

development:
  <<: *default
  database: kylas_customer_success_development

test:
  <<: *default
  database: kylas_customer_success_test

production:
  <<: *default
  database: kylas_customer_success_production
  username: <%= Rails.application.credentials.dig(:database, :username) || 'postgres' %>
  password: <%= Rails.application.credentials.dig(:database, :password) || 'password' %>
  host: <%= Rails.application.credentials.dig(:database, :host) || 'localhost' %>
  port: <%= Rails.application.credentials.dig(:database, :port) || 5432 %>
```

### Migration Management

The application includes comprehensive database migrations:

```bash
# Check migration status
rails db:migrate:status

# Run pending migrations
rails db:migrate

# Rollback migrations (if needed)
rails db:rollback STEP=1

# Reset database (development only)
rails db:drop db:create db:migrate db:seed
```

### Database Verification

After setup, verify your database is working correctly:

```bash
# Test database connection
rails runner "puts ActiveRecord::Base.connection.execute('SELECT version()').first"

# Check all tables exist
rails runner "puts ActiveRecord::Base.connection.tables.sort"

# Verify migrations are up to date
rails db:migrate:status
```

## 🚀 Setup Instructions

### Prerequisites

- **Ruby 3.1.0**: Install using rbenv or rvm
- **PostgreSQL 9.3+**: Database server (or Docker for containerized setup)
- **Node.js**: For asset compilation
- **Git**: Version control
- **Docker & Docker Compose** (Optional but recommended): For easy database setup

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd kylas_customer_success
   ```

2. **Install Dependencies**
   ```bash
   bundle install
   ```

3. **Database Setup**

   **Option 1: Automated Setup (Recommended)**
   ```bash
   # Run the automated database setup script
   bin/setup_database
   ```

   **Option 2: Docker Setup (Preferred for Development)**
   ```bash
   # Start PostgreSQL with Docker Compose
   docker-compose up -d postgres

   # Wait for PostgreSQL to be ready, then run setup
   bin/setup_database
   ```

   **Option 3: Manual Setup**
   ```bash
   # Configure database credentials in config/database.yml
   rails db:create
   rails db:migrate
   rails db:seed

   # Setup test database
   RAILS_ENV=test rails db:create
   RAILS_ENV=test rails db:migrate
   ```

4. **Environment Configuration**
   ```bash
   # Set up Rails credentials
   EDITOR="code --wait" rails credentials:edit

   # Add required credentials:
   # aws:
   #   access_key_id: your_aws_access_key
   #   secret_access_key: your_aws_secret_key
   #   bucket_name: your_s3_bucket
   #   region: your_aws_region
   #   host: your_aws_endpoint
   # chargebee:
   #   api_key: your_chargebee_api_key
   #   url: your_chargebee_url
   # log_dna_ingestion_key: your_logdna_key
   ```

5. **Start the Application**
   ```bash
   rails server
   ```

6. **Access the Application**
   - Open browser to `http://localhost:3000`
   - Create initial admin user through Rails console:
   ```ruby
   rails console
   User.create!(
     name: "Admin User",
     email: "<EMAIL>",
     password: "password123",
     role: "ADMIN"
   )
   ```

### Development Setup

1. **Install Development Dependencies**
   ```bash
   bundle install --with development test
   ```

2. **Run Tests**
   ```bash
   rspec
   ```

3. **Code Coverage**
   ```bash
   rspec --format documentation
   open coverage/index.html
   ```

4. **Linting and Code Quality**
   ```bash
   # Add rubocop if needed
   bundle exec rubocop
   ```

## 🔗 API Integrations

### AWS S3 Integration

The application integrates with AWS S3 for automated data synchronization:

- **Onboarding Data**: Daily sync of customer onboarding information
- **Usage Data**: Real-time usage statistics and analytics
- **File Storage**: Secure storage of application data and backups
- **Environment-specific**: Separate buckets for production and QA environments

**Configuration:**
```ruby
# S3 bucket configuration in config/initializers/constants.rb
S3_BUCKET_NAME = Rails.application.credentials.aws[:bucket_name]
S3_ONBOARDING_SHEET_KEY = Rails.env == 'production' ? 'onboarding.json' : 'qa/onboarding.json'
S3_USAGE_SHEET_KEY = Rails.env == 'production' ? 'daily-usage.json' : 'qa/daily-usage.json'
```

### Chargebee Integration

Real-time billing and subscription management:

- **Subscription Data**: Fetch customer subscription details
- **Plan Information**: Retrieve current plan and billing status
- **Payment History**: Access payment and renewal information
- **API Authentication**: Secure API key-based authentication

**Usage Example:**
```ruby
# Fetch subscription details
subscription_data = Chargebee::Subscription.get(subscription_id)
```

### Kylas Marketplace API

Integration with Kylas marketplace for app data:

- **App Catalog**: Retrieve available marketplace applications
- **Installation Status**: Track app installation and usage
- **Real-time Updates**: Live synchronization of marketplace data
- **Error Handling**: Robust error handling for API failures

**Service Implementation:**
```ruby
# Marketplace apps data service
marketplace_apps = MarketplaceAppsData.call
```

## 📦 Deployment

### Production Deployment

The application uses Mina for fast, efficient deployments:

1. **Deployment Configuration**
   ```bash
   # Configure deployment settings in config/deploy.rb
   mina setup
   ```

2. **Deploy Application**
   ```bash
   mina deploy
   ```

3. **Database Migration**
   ```bash
   # Migrations are run automatically during deployment
   mina rails:db_migrate
   ```

4. **Asset Compilation**
   ```bash
   # Assets are precompiled during deployment
   mina rails:assets_precompile
   ```

### Environment Configuration

- **Production**: Optimized for performance and security
- **Staging**: Mirror of production for testing
- **Development**: Local development environment
- **Test**: Isolated testing environment

### Monitoring and Logging

- **Honeybadger**: Error tracking and performance monitoring
- **LogDNA**: Centralized logging and log analysis
- **Application Logs**: Structured logging with request context
- **Performance Metrics**: Response time and throughput monitoring

## 🧪 Testing

### Test Suite Overview

The application includes comprehensive testing with RSpec:

- **Model Tests**: Unit tests for all ActiveRecord models
- **Controller Tests**: Integration tests for all controllers
- **Service Tests**: Tests for business logic in service objects
- **Request Tests**: End-to-end API testing
- **Feature Tests**: User workflow testing

### Running Tests

```bash
# Run all tests
rspec

# Run specific test files
rspec spec/models/user_spec.rb
rspec spec/controllers/users_controller_spec.rb
rspec spec/services/read_tenant_data_spec.rb

# Run tests with coverage
rspec --format documentation
open coverage/index.html
```

### Test Configuration

- **Factory Bot**: Test data generation with realistic factories
- **Faker**: Dynamic fake data for comprehensive testing
- **WebMock**: HTTP request stubbing for external API testing
- **SimpleCov**: Code coverage analysis and reporting

### Key Test Areas

1. **Authentication & Authorization**
   - User login/logout flows
   - Role-based access control
   - Invitation system testing

2. **Data Synchronization**
   - AWS S3 integration testing
   - External API response handling
   - Error scenario testing

3. **Business Logic**
   - Account management workflows
   - Customer requirement tracking
   - RGS input validation

## ⚙️ Background Jobs & Scheduling

### Cron Jobs

The application uses the `whenever` gem for scheduled tasks:

```ruby
# config/schedule.rb
every :day, at: ['12:30 pm'] do
  rake 'system_update:update'
end
```

### Data Synchronization Jobs

1. **Daily Data Sync**
   - Fetches onboarding data from S3
   - Updates usage statistics
   - Processes customer account information
   - Handles error scenarios and retries

2. **System Update Task**
   ```bash
   # Manual execution
   rake system_update:update
   ```

### Job Monitoring

- **Error Handling**: Comprehensive error logging and notification
- **Retry Logic**: Automatic retry for failed jobs
- **Performance Tracking**: Job execution time monitoring
- **Status Reporting**: Job success/failure reporting

## 📊 Data Flow Diagrams

### Data Synchronization Process

The application implements a robust data synchronization system that processes external data daily:

1. **External Data Sources**: Kylas main platform exports customer and usage data
2. **AWS S3 Storage**: Centralized storage for onboarding and usage JSON files
3. **Scheduled Processing**: Daily cron jobs trigger data synchronization
4. **Service Layer**: Specialized services handle data processing and API integration
5. **Database Updates**: Processed data updates PostgreSQL with historical tracking
6. **Error Handling**: Comprehensive error tracking and retry mechanisms

## 🔧 Configuration

### Environment Variables

The application requires several environment configurations:

#### AWS Configuration
```yaml
# config/credentials.yml.enc
aws:
  access_key_id: your_aws_access_key_id
  secret_access_key: your_aws_secret_access_key
  bucket_name: your_s3_bucket_name
  region: us-east-1
  host: https://s3.amazonaws.com
```

#### Chargebee Configuration
```yaml
chargebee:
  api_key: your_chargebee_api_key
  url: https://kylas.chargebee.com/api/v2/subscriptions
```

#### Logging Configuration
```yaml
log_dna_ingestion_key: your_logdna_ingestion_key
```

### Application Constants

Key application constants are defined in `config/initializers/constants.rb`:

- **User Roles**: ADMIN, USER
- **Plan Types**: EMBARK, ELEVATE, EXCEED
- **Entity Categories**: LEAD, DEAL, CONTACT, COMPANY, etc.
- **Requirement Status**: ACCEPTED, NOT_DOABLE, IN_PROGRESS, COMPLETED, PENDING
- **File Paths**: S3 bucket keys for different environments

## 📈 Performance Considerations

### Database Optimization
- **Indexing**: Proper database indexes on frequently queried columns
- **Pagination**: Efficient pagination using Pagy gem
- **Query Optimization**: Optimized ActiveRecord queries to minimize N+1 problems

### Caching Strategy
- **Application Caching**: Rails caching for frequently accessed data
- **Asset Optimization**: Precompiled assets for production performance
- **CDN Integration**: Asset delivery optimization

### Monitoring & Alerting
- **Error Tracking**: Real-time error monitoring with Honeybadger
- **Performance Monitoring**: Response time and throughput tracking
- **Log Analysis**: Centralized logging with LogDNA for debugging and analysis

## 🔒 Security Features

### Authentication Security
- **Strong Password Requirements**: Enforced password complexity
- **Session Management**: Secure session handling with proper timeouts
- **Invitation-Only Registration**: Controlled user registration via admin invitations

### Data Protection
- **Role-Based Access Control**: Strict permission-based feature access
- **Request Logging**: Comprehensive audit trail of all user actions
- **IP Tracking**: Request origin tracking for security analysis
- **Encrypted Credentials**: Rails encrypted credentials for sensitive data

### API Security
- **Secure External Integrations**: Encrypted API communications
- **Error Handling**: Secure error messages without sensitive data exposure
- **Input Validation**: Comprehensive input validation and sanitization

## 🐛 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL service status
sudo service postgresql status

# Restart PostgreSQL if needed
sudo service postgresql restart

# Verify database configuration
rails db:migrate:status
```

#### AWS S3 Connection Problems
```bash
# Test S3 connectivity
rails console
> Aws::S3::Client.new.list_buckets

# Check credentials
> Rails.application.credentials.aws
```

#### External API Failures
```bash
# Test Chargebee API
rails console
> Chargebee::Subscription.get('test_subscription_id')

# Test Marketplace API
> MarketplaceAppsData.call
```

#### Cron Job Issues
```bash
# Check cron job status
crontab -l

# Update cron jobs
whenever --update-crontab

# View cron logs
tail -f log/system_update.log
```

### Debugging Tips

1. **Check Application Logs**
   ```bash
   tail -f log/development.log
   tail -f log/production.log
   ```

2. **Monitor Error Tracking**
   - Check Honeybadger dashboard for real-time errors
   - Review LogDNA for detailed request logs

3. **Database Debugging**
   ```bash
   rails console
   > ActiveRecord::Base.logger = Logger.new(STDOUT)
   ```

4. **Service Testing**
   ```bash
   # Test individual services
   rails console
   > ReadTenantData.call
   > MarketplaceAppsData.call
   ```

## 🤝 Contributing

### Development Workflow

1. **Fork the Repository**
   ```bash
   git clone <your-fork-url>
   cd kylas_customer_success
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Changes**
   - Follow Rails conventions and best practices
   - Write comprehensive tests for new features
   - Update documentation as needed

4. **Run Tests**
   ```bash
   rspec
   bundle exec rubocop
   ```

5. **Submit Pull Request**
   - Provide clear description of changes
   - Include test coverage for new features
   - Ensure all CI checks pass

### Code Standards

- **Ruby Style Guide**: Follow community Ruby style guidelines
- **Rails Conventions**: Adhere to Rails naming and structure conventions
- **Test Coverage**: Maintain high test coverage (>90%)
- **Documentation**: Update README and inline documentation for changes

### Commit Guidelines

```bash
# Use conventional commit format
git commit -m "feat: add customer requirement status tracking"
git commit -m "fix: resolve S3 connection timeout issue"
git commit -m "docs: update API integration documentation"
```

## 📚 Additional Resources

### Documentation Links
- [Rails Guides](https://guides.rubyonrails.org/)
- [Devise Documentation](https://github.com/heartcombo/devise)
- [AWS SDK for Ruby](https://docs.aws.amazon.com/sdk-for-ruby/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### External Service Documentation
- [Chargebee API Documentation](https://apidocs.chargebee.com/)
- [Kylas API Documentation](https://api.kylas.io/docs)
- [Honeybadger Documentation](https://docs.honeybadger.io/)
- [LogDNA Documentation](https://docs.logdna.com/)

## 📞 Support

### Getting Help

1. **Internal Documentation**: Check this README and inline code documentation
2. **Error Tracking**: Monitor Honeybadger for real-time error information
3. **Logs**: Review application logs in LogDNA for detailed debugging
4. **Team Communication**: Contact the development team for assistance

### Reporting Issues

When reporting issues, please include:
- **Environment**: Development, staging, or production
- **Steps to Reproduce**: Detailed steps to recreate the issue
- **Expected Behavior**: What should happen
- **Actual Behavior**: What actually happens
- **Error Messages**: Any error messages or stack traces
- **Browser/System Info**: If applicable

---

## 📄 License

This project is proprietary software developed for Kylas Customer Success operations.

## 🏢 Project Information

- **Project Name**: Kylas Customer Success Portal
- **Version**: 1.0.0
- **Ruby Version**: 3.1.0
- **Rails Version**: 7.0.4+
- **Database**: PostgreSQL 9.3+
- **Last Updated**: 2024

---

*This README provides comprehensive documentation for the Kylas Customer Success Portal. For additional information or support, please contact the development team.*

