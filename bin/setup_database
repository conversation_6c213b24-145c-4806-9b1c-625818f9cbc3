#!/usr/bin/env ruby
require "fileutils"

# path to your application root.
APP_ROOT = File.expand_path("..", __dir__)

def system!(*args)
  system(*args) || abort("\n== Command #{args} failed ==")
end

def check_postgres_connection
  puts "== Checking PostgreSQL connection =="
  begin
    require 'pg'
    conn = PG.connect(host: 'localhost', port: 5432, user: 'postgres', password: 'password')
    conn.close
    puts "✓ PostgreSQL connection successful"
    true
  rescue PG::ConnectionBad => e
    puts "✗ PostgreSQL connection failed: #{e.message}"
    false
  rescue LoadError
    puts "✗ pg gem not installed. Run 'bundle install' first."
    false
  end
end

def start_docker_postgres
  puts "== Starting PostgreSQL with Docker Compose =="
  if system("docker-compose up -d postgres")
    puts "✓ PostgreSQL started with Docker"
    
    # Wait for PostgreSQL to be ready
    puts "== Waiting for PostgreSQL to be ready =="
    30.times do |i|
      if system("docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1")
        puts "✓ PostgreSQL is ready"
        return true
      end
      print "."
      sleep 1
    end
    puts "\n✗ PostgreSQL did not become ready in time"
    false
  else
    puts "✗ Failed to start PostgreSQL with Docker"
    false
  end
end

FileUtils.chdir APP_ROOT do
  puts "== Kylas Customer Success Database Setup =="
  
  # Check if PostgreSQL is already running
  unless check_postgres_connection
    puts "\n== PostgreSQL not running. Attempting to start with Docker =="
    
    # Check if Docker is available
    unless system("docker --version > /dev/null 2>&1")
      puts "✗ Docker not found. Please install Docker or start PostgreSQL manually."
      puts "  Manual setup: Install PostgreSQL and create databases:"
      puts "  - kylas_customer_success_development"
      puts "  - kylas_customer_success_test"
      puts "  - kylas_customer_success_staging"
      puts "  - kylas_customer_success_production"
      exit 1
    end
    
    # Check if docker-compose is available
    unless system("docker-compose --version > /dev/null 2>&1")
      puts "✗ docker-compose not found. Please install docker-compose."
      exit 1
    end
    
    unless start_docker_postgres
      puts "✗ Failed to start PostgreSQL. Please check Docker setup."
      exit 1
    end
  end
  
  puts "\n== Installing dependencies =="
  system! "gem install bundler --conservative"
  system("bundle check") || system!("bundle install")
  
  puts "\n== Setting up databases =="
  
  # Create databases if they don't exist
  puts "== Creating databases =="
  system! "bin/rails db:create"
  
  # Run migrations
  puts "== Running migrations =="
  system! "bin/rails db:migrate"
  
  # Setup test database
  puts "== Setting up test database =="
  system! "RAILS_ENV=test bin/rails db:create"
  system! "RAILS_ENV=test bin/rails db:migrate"
  
  # Load seeds if available
  if File.exist?("db/seeds.rb") && !File.read("db/seeds.rb").strip.empty?
    puts "== Loading seed data =="
    system! "bin/rails db:seed"
  end
  
  puts "\n== Verifying database setup =="
  system! "bin/rails db:migrate:status"
  
  puts "\n== Database setup completed successfully! =="
  puts "✓ Development database: kylas_customer_success_development"
  puts "✓ Test database: kylas_customer_success_test"
  puts "✓ All migrations applied"
  puts "\nYou can now start the application with: bin/rails server"
end
