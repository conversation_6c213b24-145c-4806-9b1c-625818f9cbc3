#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative '../config/environment'

puts "🔍 Verifying Sample Data for Kylas Customer Success Portal"
puts "=" * 60

# Verify admin user
admin_user = User.find_by(email: '<EMAIL>')
if admin_user
  puts "✅ Admin user created successfully:"
  puts "   Email: #{admin_user.email}"
  puts "   Name: #{admin_user.name}"
  puts "   Role: #{admin_user.role}"
  puts "   Password: test@123"
else
  puts "❌ Admin user not found!"
  exit 1
end

puts "\n📊 Sample Data Summary:"
puts "-" * 30

data_summary = {
  'Users' => User.count,
  'Tenants' => Tenant.count,
  'Account Details' => AccountDetail.count,
  'Usage Records' => Usage.count,
  'Usage History' => UsageHistory.count,
  'Reports' => Report.count,
  'Report Filters' => ReportFilter.count,
  'Report Results' => ReportResult.count,
  'Report Shares' => ReportShare.count,
  'Customer Asks' => CustomerAsk.count,
  'Customer Requirements' => CustomerRequirement.count,
  'RGS Inputs' => RgsInput.count,
  'Entities' => Entity.count,
  'Marketplace Apps' => MarketplaceApp.count
}

data_summary.each do |model, count|
  puts "   #{model.ljust(20)}: #{count}"
end

puts "\n📈 Report Types Created:"
puts "-" * 30
Report.all.each do |report|
  puts "   • #{report.name} (#{report.report_type})"
  puts "     Data Source: #{report.data_source}"
  puts "     Public: #{report.is_public? ? 'Yes' : 'No'}"
  puts "     Scheduled: #{report.is_scheduled? ? 'Yes' : 'No'}"
  puts ""
end

puts "\n🏢 Sample Tenants:"
puts "-" * 30
Tenant.includes(:account_detail).each do |tenant|
  account = tenant.account_detail
  puts "   • #{tenant.name} (ID: #{tenant.kylas_tenant_id})"
  if account
    puts "     Plan: #{account.plan_name}"
    puts "     Industry: #{account.industry}"
    puts "     Active Users: #{account.active_user_count}"
    puts "     Total Records: #{account.lead_count + account.deal_count + account.contact_count}"
  end
  puts ""
end

puts "\n👥 User Accounts:"
puts "-" * 30
User.all.each do |user|
  puts "   • #{user.name} (#{user.email})"
  puts "     Role: #{user.role}"
  puts "     Password: test@123"
  puts ""
end

puts "\n🔗 Report Sharing:"
puts "-" * 30
ReportShare.includes(:report, :user, :shared_by).each do |share|
  puts "   • #{share.report.name}"
  puts "     Shared with: #{share.user.name} (#{share.permission_level})"
  puts "     Shared by: #{share.shared_by.name}"
  puts "     Can Export: #{share.can_export? ? 'Yes' : 'No'}"
  puts ""
end

puts "\n🎯 Usage Statistics:"
puts "-" * 30
total_usage = Usage.count
active_users = Usage.where(logged_in: true).count
inactive_users = Usage.where(logged_in: false).count

puts "   Total Usage Records: #{total_usage}"
puts "   Active User Sessions: #{active_users}"
puts "   Inactive User Sessions: #{inactive_users}"
puts "   Activity Rate: #{((active_users.to_f / total_usage) * 100).round(1)}%"

# Check for recent usage data
recent_usage = Usage.where('date >= ?', 7.days.ago).count
puts "   Recent Usage (7 days): #{recent_usage}"

puts "\n✅ Sample Data Verification Complete!"
puts "\n🚀 Ready to test the application!"
puts "\nLogin Details:"
puts "   URL: http://localhost:3000"
puts "   Email: <EMAIL>"
puts "   Password: test@123"
puts "\nAdditional Test Users:"
User.where.not(email: '<EMAIL>').each do |user|
  puts "   Email: #{user.email}, Password: test@123 (#{user.role})"
end

puts "\n📝 Next Steps:"
puts "   1. Start the Rails server: rails server"
puts "   2. Visit http://localhost:3000"
puts "   3. Login with the credentials above"
puts "   4. Navigate to Reports section to test all features"
puts "   5. Verify all report types work without errors"
puts "   6. Test report filtering, sharing, and export features"
