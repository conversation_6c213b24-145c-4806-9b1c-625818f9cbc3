#!/usr/bin/env ruby
# frozen_string_literal: true

require 'net/http'
require 'uri'
require 'json'
require 'cgi'

class APITester
  def initialize(base_url = 'http://localhost:3000')
    @base_url = base_url
    @session_cookie = nil
    @csrf_token = nil
  end

  def run_tests
    puts "🧪 Testing Kylas Customer Success Portal APIs"
    puts "=" * 60

    # Test basic connectivity
    test_connectivity

    # Test authentication
    test_authentication

    # Test reports endpoints
    test_reports_endpoints

    # Test account details endpoints
    test_account_details_endpoints

    # Test user management endpoints
    test_user_endpoints

    puts "\n✅ API Testing Complete!"
  end

  private

  def test_connectivity
    puts "\n🔗 Testing Basic Connectivity"
    puts "-" * 30

    response = make_request('GET', '/')
    if response.code == '302' && response['Location'].include?('sign_in')
      puts "✅ Root path redirects to login (expected)"
    else
      puts "❌ Unexpected response from root path: #{response.code}"
    end
  end

  def test_authentication
    puts "\n🔐 Testing Authentication"
    puts "-" * 30

    # Get login page to extract CSRF token
    response = make_request('GET', '/users/sign_in')
    if response.code == '200'
      puts "✅ Login page accessible"
      extract_csrf_token(response.body)
    else
      puts "❌ Cannot access login page: #{response.code}"
      return
    end

    # Attempt login
    login_data = {
      'user[email]' => '<EMAIL>',
      'user[password]' => 'test@123',
      'authenticity_token' => @csrf_token
    }

    response = make_request('POST', '/users/sign_in', login_data)
    if response.code == '302'
      puts "✅ Login successful (redirected)"
      extract_session_cookie(response)
    else
      puts "❌ Login failed: #{response.code}"
      puts "Response body: #{response.body[0..200]}..."
    end
  end

  def test_reports_endpoints
    puts "\n📊 Testing Reports Endpoints"
    puts "-" * 30

    return unless @session_cookie

    # Test reports index
    response = make_authenticated_request('GET', '/reports')
    if response.code == '200'
      puts "✅ Reports index accessible"
    else
      puts "❌ Reports index failed: #{response.code}"
      puts "Response: #{response.body[0..200]}..."
    end

    # Test reports with filters
    response = make_authenticated_request('GET', '/reports?type=usage')
    if response.code == '200'
      puts "✅ Reports filtering by type works"
    else
      puts "❌ Reports filtering failed: #{response.code}"
    end

    response = make_authenticated_request('GET', '/reports?data_source=onboarding')
    if response.code == '200'
      puts "✅ Reports filtering by data source works"
    else
      puts "❌ Reports data source filtering failed: #{response.code}"
    end

    # Test individual report
    report_id = get_first_report_id
    if report_id
      response = make_authenticated_request('GET', "/reports/#{report_id}")
      if response.code == '200'
        puts "✅ Individual report view accessible"
      else
        puts "❌ Individual report view failed: #{response.code}"
      end

      # Test report generation
      response = make_authenticated_request('POST', "/reports/#{report_id}/generate")
      if response.code == '302'
        puts "✅ Report generation triggered"
      else
        puts "❌ Report generation failed: #{response.code}"
      end

      # Test report export
      response = make_authenticated_request('GET', "/reports/#{report_id}/export?format=csv")
      if response.code == '200' || response.code == '302'
        puts "✅ Report export accessible"
      else
        puts "❌ Report export failed: #{response.code}"
      end
    end

    # Test new report form
    response = make_authenticated_request('GET', '/reports/new')
    if response.code == '200'
      puts "✅ New report form accessible"
    else
      puts "❌ New report form failed: #{response.code}"
    end
  end

  def test_account_details_endpoints
    puts "\n🏢 Testing Account Details Endpoints"
    puts "-" * 30

    return unless @session_cookie

    # Test account details index
    response = make_authenticated_request('GET', '/account-details')
    if response.code == '200'
      puts "✅ Account details index accessible"
    else
      puts "❌ Account details index failed: #{response.code}"
    end

    # Test individual account detail
    account_id = get_first_account_id
    if account_id
      response = make_authenticated_request('GET', "/account-details/#{account_id}")
      if response.code == '200'
        puts "✅ Individual account detail accessible"
      else
        puts "❌ Individual account detail failed: #{response.code}"
      end

      # Test plan details
      response = make_authenticated_request('GET', "/account-details/#{account_id}/plan-details")
      if response.code == '200'
        puts "✅ Plan details accessible"
      else
        puts "❌ Plan details failed: #{response.code}"
      end

      # Test RGS inputs
      response = make_authenticated_request('GET', "/account-details/#{account_id}/rgs-inputs")
      if response.code == '200'
        puts "✅ RGS inputs accessible"
      else
        puts "❌ RGS inputs failed: #{response.code}"
      end

      # Test customer asks
      response = make_authenticated_request('GET', "/account-details/#{account_id}/customer-asks")
      if response.code == '200'
        puts "✅ Customer asks accessible"
      else
        puts "❌ Customer asks failed: #{response.code}"
      end
    end
  end

  def test_user_endpoints
    puts "\n👥 Testing User Management Endpoints"
    puts "-" * 30

    return unless @session_cookie

    # Test users index
    response = make_authenticated_request('GET', '/users')
    if response.code == '200'
      puts "✅ Users index accessible"
    else
      puts "❌ Users index failed: #{response.code}"
    end

    # Test users list JSON
    response = make_authenticated_request('GET', '/users/list.json')
    if response.code == '200'
      puts "✅ Users JSON list accessible"
    else
      puts "❌ Users JSON list failed: #{response.code}"
    end

    # Test new user form
    response = make_authenticated_request('GET', '/users/new')
    if response.code == '200'
      puts "✅ New user form accessible"
    else
      puts "❌ New user form failed: #{response.code}"
    end
  end

  def make_request(method, path, data = nil)
    uri = URI("#{@base_url}#{path}")
    http = Net::HTTP.new(uri.host, uri.port)
    
    case method
    when 'GET'
      request = Net::HTTP::Get.new(uri)
    when 'POST'
      request = Net::HTTP::Post.new(uri)
      if data
        request.set_form_data(data)
      end
    end

    request['Cookie'] = @session_cookie if @session_cookie
    
    http.request(request)
  end

  def make_authenticated_request(method, path, data = nil)
    return nil unless @session_cookie
    make_request(method, path, data)
  end

  def extract_csrf_token(html)
    match = html.match(/name="authenticity_token" value="([^"]+)"/)
    @csrf_token = match[1] if match
  end

  def extract_session_cookie(response)
    set_cookie = response['Set-Cookie']
    if set_cookie
      @session_cookie = set_cookie.split(';').first
    end
  end

  def get_first_report_id
    return nil unless @session_cookie
    
    response = make_authenticated_request('GET', '/reports')
    return nil unless response.code == '200'
    
    # Simple regex to find first report ID in the HTML
    match = response.body.match(/\/reports\/(\d+)/)
    match[1] if match
  end

  def get_first_account_id
    return nil unless @session_cookie
    
    response = make_authenticated_request('GET', '/account-details')
    return nil unless response.code == '200'
    
    # Simple regex to find first account ID in the HTML
    match = response.body.match(/\/account-details\/(\d+)/)
    match[1] if match
  end
end

# Run the tests
if __FILE__ == $0
  tester = APITester.new
  tester.run_tests
end
