# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User, type: :model do
  before do
    @user = create(:admin)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@user).to be_valid
    end
  end
  
  context 'When attributes are invalid' do
    it 'Should be invalid with repeating email' do
      second_user = build(:user, email: @user.email)
      expect(second_user).to be_invalid
      expect(second_user.errors.full_messages.first).to eq("Email has already been taken")
    end

    it 'Should be invalid with no email' do
      @user.email = nil
      expect(@user).to be_invalid
      expect(@user.errors.full_messages.first).to eq("Email can't be blank")
    end

    it 'Should be invalid with invalid email' do
      @user.email = 'firstname'
      expect(@user).to be_invalid
      expect(@user.errors.full_messages.first).to eq('Email is invalid')
    end

    it 'Should be invalid with no name' do
      @user.name = nil
      expect(@user).to be_invalid
      expect(@user.errors.full_messages.first).to eq("Name can't be blank")
    end

    it 'Should be invalid with invalid password' do
      @user.password = 'bad'
      expect(@user).to be_invalid
      expect(@user.errors.full_messages.first).to eq('Password is too short (minimum is 6 characters)')
    end

    it 'Should be invalid with invalid role' do
      @user.role = 'bad'
      expect(@user).to be_invalid
      expect(@user.errors.full_messages.first).to eq('Role is not included in the list')
    end
  end
end
