# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Tenant, type: :model do
  before(:all) do
    @tenant = create(:tenant)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@tenant).to be_valid
    end

    it 'Should have one account detail' do
      t = Tenant.reflect_on_association(:account_detail)
      expect(t.macro).to eq(:has_one)
    end
  end

  context 'association' do
    it 'should have has_many association with requirement' do
      association = Tenant.reflect_on_association(:rgs_input)
      expect(association.macro).to eq(:has_one)
    end

    it 'should have has_one association with plan_details' do
      association = Tenant.reflect_on_association(:plan_detail)
      expect(association.macro).to eq(:has_one)
    end

    it 'should have has_many association with plan_detail_history' do
      association = Tenant.reflect_on_association(:plan_detail_history)
      expect(association.macro).to eq(:has_many)
    end
  end
end
