# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PlanDetail, type: :model do
  
  before do
    @tenant = create(:tenant)
    @plan_details = create(:plan_detail, tenant: @tenant)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@plan_details).to be_valid
    end
  end

  context 'When attributes are invalid' do
    it 'Should be invalid with no tenant_id' do
      @plan_details.tenant_id = nil
      expect(@plan_details).to be_invalid
      expect(@plan_details.errors.full_messages.first).to eq("Tenant must exist")
    end

    it 'should be invalid with invalid name' do
      second_plan = build(:plan_detail, name: 'second')
      expect(second_plan).to be_invalid
      expect(second_plan.errors.full_messages.first).to eq("Name is not included in the list")
    end

    it 'should be invalid with invalid status' do
      second_plan = build(:plan_detail, status: 'OTHER')
      expect(second_plan).to be_invalid
      expect(second_plan.errors.full_messages.first).to eq("Status is not included in the list")
    end
  end

  context 'association' do
    it 'should have belongs_to association with tenant' do
      association = PlanDetail.reflect_on_association(:tenant)
      expect(association.macro).to eq(:belongs_to)
    end
  end
end
