# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AccountDetail, type: :model do
  before do
    @tenant = create(:tenant)
    @account_detail = create(:account_detail, tenant: @tenant)
  end

  context 'when attributes are valid' do
    it 'should be valid' do
      expect(@account_detail).to be_valid
    end
  end

  context 'When attributes are invalid' do
    it 'Should be invalid with no email' do
      @account_detail.email = nil
      expect(@account_detail).to be_invalid
      expect(@account_detail.errors.full_messages.first).to eq("Email can't be blank")
    end

    it 'Should be invalid with invalid email' do
      @account_detail.email = 'firstname'
      expect(@account_detail).to be_invalid
      expect(@account_detail.errors.full_messages.first).to eq('Email is invalid')
    end

    it 'Should be invalid with no tenant_id' do
      @account_detail.tenant_id = nil
      expect(@account_detail).to be_invalid
      expect(@account_detail.errors.full_messages.first).to eq("Tenant must exist")
    end

    it 'Should be invalid if mobile number is invalid' do
      @account_detail.mobile = '+91'
      expect(@account_detail).to be_invalid
      expect(@account_detail.errors.full_messages.first).to eq('Mobile is invalid')
    end

    it 'should have one account manager' do
      t = AccountDetail.reflect_on_association(:account_manager)
      expect(t.macro).to eq(:has_one)
    end

    it 'should have one support executive' do
      t = AccountDetail.reflect_on_association(:support_executive)
      expect(t.macro).to eq(:has_one)
    end

    it 'should belong to one tenant' do
      t = AccountDetail.reflect_on_association(:tenant)
      expect(t.macro).to eq(:belongs_to)
    end
  end
end
