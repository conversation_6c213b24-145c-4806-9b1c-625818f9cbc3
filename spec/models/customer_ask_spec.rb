# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CustomerAsk, type: :model do

  before do
    @customer_ask = create(:customer_ask, tenant: create(:tenant))
  end
  
  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@customer_ask).to be_valid
    end
  end

  context 'When attributes are Invalid' do
    it 'Should be invalid with no tenant_id' do
      @customer_ask.tenant_id = nil
      expect(@customer_ask).to be_invalid
      expect(@customer_ask.errors.full_messages.first).to eq("Tenant must exist")
    end
  end

  context 'association' do
    it 'should have belongs_to association with tenant' do
      association = CustomerAsk.reflect_on_association(:tenant)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'should have has_many association with customer_requirements' do
      association = CustomerAsk.reflect_on_association(:customer_requirements)
      expect(association.macro).to eq(:has_many)
    end
  end
end 