# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RgsInput, type: :model do

  before do
    @rgs_input = create(:rgs_input, tenant: create(:tenant))
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@rgs_input).to be_valid
    end
  end

  context 'When attributes are Invalid' do
    it 'Should be invalid with no tenant_id' do
      @rgs_input.tenant_id = nil
      expect(@rgs_input).to be_invalid
      expect(@rgs_input.errors.full_messages.first).to eq("Tenant must exist")
    end
  end

  context 'association' do
    it 'should have belongs_to association with tenant' do
      association = RgsInput.reflect_on_association(:tenant)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'should have has_many association with entities' do
      association = RgsInput.reflect_on_association(:entities)
      expect(association.macro).to eq(:has_many)
    end

    it 'should have has_many association with marketplace' do
      association = RgsInput.reflect_on_association(:marketplace_apps)
      expect(association.macro).to eq(:has_many)
    end
  end
end
