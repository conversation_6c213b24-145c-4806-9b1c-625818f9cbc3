# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Entity, type: :model do

  before do
    @requirement = create(:rgs_input, tenant: create(:tenant))
    @entity = create(:entity, rgs_input: @requirement)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@entity).to be_valid
    end
  end

  context 'When attributes are Invalid' do
    it 'Should be invalid with no rgs_input' do
      @entity.rgs_input_id = nil
      expect(@entity).to be_invalid
      expect(@entity.errors.full_messages.first).to eq("Rgs input must exist")
    end

    it 'Should be invalid with no critical' do
      @entity.critical = nil
      expect(@entity).to be_invalid
      expect(@entity.errors.full_messages.first).to eq("Critical is not included in the list")
    end

    it 'Should be invalid with no expected_volume' do
      @entity.expected_volume = nil
      expect(@entity).to be_invalid
      expect(@entity.errors.full_messages.first).to eq("Expected volume can't be blank")
    end

    it 'Should be invalid with no frequency' do
      @entity.frequency = nil
      expect(@entity).to be_invalid
      expect(@entity.errors.full_messages.first).to eq("Frequency is not included in the list")
    end

    it 'should be invalid with invalid category' do
      second_plan = build(:entity, category: 'second',rgs_input: create(:rgs_input, tenant: create(:tenant)) )
      expect(second_plan).to be_invalid
      expect(second_plan.errors.full_messages.first).to eq("Category second is not a valid category")
    end
  end

  context 'association' do
    it 'should have has_many association with account' do
      association = Entity.reflect_on_association(:rgs_input)
      expect(association.macro).to eq(:belongs_to)
    end
  end
end
