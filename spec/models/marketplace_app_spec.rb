# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MarketplaceApp, type: :model do

  before do
    @requirement = create(:rgs_input, tenant: create(:tenant))
    @marketplaceApp = create(:marketplace_app, rgs_input: @requirement)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@marketplaceApp).to be_valid
    end
  end

  context 'When attributes are invalid' do
    it 'Should be invalid with no rgs_input' do
      @marketplaceApp.rgs_input_id = nil
      expect(@marketplaceApp).to be_invalid
      expect(@marketplaceApp.errors.full_messages.first).to eq("Rgs input must exist")
    end

    it 'Should be invalid with no integration' do
      @marketplaceApp.integrated = nil
      expect(@marketplaceApp).to be_invalid
      expect(@marketplaceApp.errors.full_messages.first).to eq("Integrated  is not a valid value")
    end

    it 'should be invalid with no name' do
      @marketplaceApp.name = nil
      expect(@marketplaceApp).to be_invalid
      expect(@marketplaceApp.errors.full_messages.first).to eq("Name can't be blank")
    end
  end

  context 'association' do
    it 'should have has_many association with account' do
      association = MarketplaceApp.reflect_on_association(:rgs_input)
      expect(association.macro).to eq(:belongs_to)
    end
  end
end
