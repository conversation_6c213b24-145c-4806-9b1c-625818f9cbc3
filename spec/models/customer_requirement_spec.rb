# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CustomerRequirement, type: :model do

  before do
    @requirement = create(:customer_ask, tenant: create(:tenant))
    @customerRequirement = create(:customer_requirement, customer_ask: @requirement)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@customerRequirement).to be_valid
    end
  end

  context 'When attributes are Invalid' do
    it 'Should be invalid with no customer_ask' do
      @customerRequirement.customer_ask_id = nil
      expect(@customerRequirement).to be_invalid
      expect(@customerRequirement.errors.full_messages.first).to eq("Customer ask must exist")
    end
  end 

  context 'When attributes are Invalid' do
    it 'Should be invalid with no tenant_id' do
      @requirement.tenant_id = nil
      expect(@requirement).to be_invalid
      expect(@requirement.errors.full_messages.first).to eq("Tenant must exist")
    end

    it 'Should be invalid with no description' do
      @customerRequirement.description = nil
      expect(@customerRequirement).to be_invalid
      expect(@customerRequirement.errors.full_messages.first).to eq("Description can't be blank")
    end
    it 'Should be invalid with invalid status' do
      @customerRequirement.status = "NOT_ACCEPTED"
      expect(@customerRequirement).to be_invalid
      expect(@customerRequirement.errors.full_messages.first).to eq("Status NOT_ACCEPTED is not a valid status")
    end
    
    it 'Should be invalid with invalid category' do
      @customerRequirement.category = "PERSON"
      expect(@customerRequirement).to be_invalid
      expect(@customerRequirement.errors.full_messages.first).to eq("Category is not included in the list")
    end
  end

  context 'association' do
    it 'should have has_many association with customer aks' do
      association = CustomerRequirement.reflect_on_association(:customer_ask)
      expect(association.macro).to eq(:belongs_to)
    end
  end
end 
