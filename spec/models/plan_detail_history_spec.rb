# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PlanDetailHistory, type: :model do
  
  before do
    @tenant = create(:tenant)
    @plan_detail_history = create(:plan_detail_history, tenant: @tenant)
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@plan_detail_history).to be_valid
    end
  end

  context 'When attributes are invalid' do
    it 'Should be invalid with no tenant_id' do
      @plan_detail_history.tenant_id = nil
      expect(@plan_detail_history).to be_invalid
      expect(@plan_detail_history.errors.full_messages.first).to eq("Tenant must exist")
    end

    it 'Should be invalid with last paid_on' do
      second_plan = create(:plan_detail_history, tenant: create(:tenant))
      second_plan.last_paid_on = nil
      expect(second_plan).to be_invalid
      expect(second_plan.errors.full_messages.first).to eq("Last paid on can't be blank")
    end

    it 'should be invalid with invalid name' do
      second_plan = build(:plan_detail_history, name: 'second')
      expect(second_plan).to be_invalid
      expect(second_plan.errors.full_messages.first).to eq("Name is not included in the list")
    end

    it 'should be invalid with invalid status' do
      second_plan = build(:plan_detail_history, status: 'OTHER')
      expect(second_plan).to be_invalid
      expect(second_plan.errors.full_messages.first).to eq("Status is not included in the list")
    end
  end

  context 'association' do
    it 'should have belongs_to association with tenant' do
      association = PlanDetail.reflect_on_association(:tenant)
      expect(association.macro).to eq(:belongs_to)
    end
  end
end
