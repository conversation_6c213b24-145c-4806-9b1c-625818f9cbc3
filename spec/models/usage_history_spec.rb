# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UsageHistory, type: :model do
  before do
    @tenant = create(:tenant)
    @usage = create(:usage_history, tenant: @tenant)
  end

  context 'When attributes are invalid' do
    it 'Should be invalid with no user_id' do
      @usage.user_id = nil
      expect(@usage).to be_invalid
      expect(@usage.errors.full_messages.first).to eq("User can't be blank")
    end

    it 'Should be invalid with no tenant_id' do
      @usage.tenant_id = nil
      expect(@usage).to be_invalid
      expect(@usage.errors.full_messages.first).to eq("Tenant must exist")
    end
  end

  context 'When attributes are valid' do
    it 'Should be valid' do
      expect(@usage).to be_valid
    end

    it 'should belong to one tenant' do
      t = Usage.reflect_on_association(:tenant)
      expect(t.macro).to eq(:belongs_to)
    end
  end
end
