# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PlanDetailsHelper, type: :helper do

  let(:user) { create(:user) }
  let(:plan_detail) { create(:plan_detail, last_updated_by_id: user.id) }

  describe '#updated_by' do
    context 'when user is present for given id' do
      it 'should return user name' do
        expect(helper.last_updated_by(plan_detail)).to eq(user.name)
      end
    end

    context 'when user is not present for given id' do
      before { user.destroy }
      it 'should return empty string' do
        expect(helper.last_updated_by(plan_detail)).to eq("Not yet updated")
      end
    end
  end
end
