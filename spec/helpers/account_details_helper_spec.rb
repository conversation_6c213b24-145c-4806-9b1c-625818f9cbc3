require 'rails_helper'

# Specs in this file have access to a helper object that includes
# the AccountDetailsHelper. For example:
#
# describe AccountDetailsHelper do
#   describe "string concat" do
#     it "concats two strings with spaces" do
#       expect(helper.concat_strings("this","that")).to eq("this that")
#     end
#   end
# end
RSpec.describe AccountDetailsHelper, type: :helper do
  describe '#link_to_account_detail' do
    before(:all) do
      @account = create(:account_detail)
    end
    context 'When name is nil' do
      it 'returns nil' do
        expect(link_to_account_detail(nil, @account)).to eq(link_to("-", account_detail_path(@account), { style: "display: block" }))
      end
    end

    context 'When name is not nil' do
      it 'returns the link to account detail with name' do
        expect(link_to_account_detail("Name", @account)).to eq(link_to("Name", account_detail_path(@account), { style: "display: block" }))
      end
    end
  end

  describe '#default_account_manager_name' do
    before(:all) do
      @user = create(:user)
      @account_with_manager = create(:account_detail, account_manager_id: @user.id)
      @account_without_manager = create(:account_detail)
    end
    context 'when account has account manager' do
      it 'returns the account manager name and email' do
        expect(default_account_manager_name(@account_with_manager)).to eq("#{@user.name} (#{@user.email})")
      end
    end
    context 'when account has no account manager' do
      it 'returns nil' do
        expect(default_account_manager_name(@account_without_manager)).to eq(nil)
      end
    end
  end

  describe '#default_support_executive_name' do
    before(:all) do
      @user = create(:user)
      @account_with_support_executive = create(:account_detail, support_executive_id: @user.id)
      @account_without_support_executive = create(:account_detail)
    end
    context 'when account has support executive' do
      it 'returns the support executive name and email' do
        expect(default_support_executive_name(@account_with_support_executive)).to eq("#{@user.name} (#{@user.email})")
      end
    end
    context 'when account has no support executive' do
      it 'returns nil' do
        expect(default_support_executive_name(@account_without_support_executive)).to eq(nil)
      end
    end
  end

  describe '#get_last_updated_by' do
    before(:all) do
      @updater = create(:user)
      @account = create(:account_detail)
      @account.last_updated_by_id = @updater.id
    end
    it 'returns the name of user wo updated latest' do
      expect(get_last_updated_by(@account)).to eq(@updater.name)
    end
  end
end
