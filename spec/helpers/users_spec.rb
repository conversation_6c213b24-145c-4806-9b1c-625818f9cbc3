# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UsersHelper, type: :helper do
  describe '#nav_link_class' do
    context 'When condition is true' do
      it 'returns the active class' do
        expect(nav_link_class(default_class: "DEFAULT", active_class: "ACTIVE", condition: true)).to eq("ACTIVE")
      end
    end

    context 'When condition is false' do
      it 'returns the default class' do
        expect(nav_link_class(default_class: "DEFAULT", active_class: "ACTIVE", condition: false)).to eq("DEFAULT")
      end
    end
  end
end
