# frozen_string_literal: true

FactoryBot.define do
  factory :usage_history do
    tenant
    user_id { rand(1..100) }
  end

  factory :usage do
    tenant
    user_id { rand(1..100) }
  end

  factory :account_detail_history do
    tenant
    email { Faker::Internet.unique.email }
    company { Faker::Company.unique.name }
    mobile { Faker::PhoneNumber.unique.cell_phone_in_e164 }
    industry { Faker::Company.unique.industry }
  end
  factory :account_detail do
    email { Faker::Internet.unique.email }
    company { Faker::Company.name }
    mobile { Faker::PhoneNumber.unique.cell_phone_in_e164 }
    tenant
  end

  factory :tenant do
    kylas_tenant_id { 123 }
    name { Faker::Name.unique.name }
  end

  factory :admin, class: 'User' do
    email { Faker::Internet.unique.email }
    name { Faker::Name.name }
    password { Faker::Internet.password(special_characters: true) }
    role { ADMIN }
  end

  factory :user, class: 'User' do
    email { Faker::Internet.unique.email }
    name { Faker::Name.name }
    password { Faker::Internet.password(special_characters: true) }
    role { USER }
  end

  factory :deactivated_user, class: 'User' do
    email { Faker::Internet.unique.email }
    name { Faker::Name.name }
    password { Faker::Internet.password(special_characters: true) }
    role { USER }
    deactivated { true }
  end

  factory :marketplace_app do
    rgs_input
    name { ["Apollo.io", "Arka Inventory", "Bulk Email Marketing"].sample }
    integrated { true }
  end

  factory :entity do
    rgs_input
    category { ENTITY_CATEGORY.sample }
    critical { true }
    frequency { ENTITY_FREQUENCY.sample }
    expected_volume { rand(10) }
  end

  factory :rgs_input do
    tenant
    total_users { rand(100) }
    total_managers { rand(100) }
  end

  factory :plan_detail do
    tenant
    name { ALLOWED_PLANS.sample }
    next_renewal { 1.month.from_now }
    ob_start { 1.week.ago }
    ob_completion { 2.days.from_now }
    add_on { "Extra features" }
    status { PLAN_STATUS.sample }
    last_updated_by_id { Faker::Number.unique.number }
  end

  factory :plan_detail_history do
    tenant
    name { ALLOWED_PLANS.sample }
    last_paid_on { Time.current }
    next_renewal { 1.month.from_now }
    ob_start { 1.week.ago }
    ob_completion { 2.days.from_now }
    add_on { "Extra features" }
    status { PLAN_STATUS.sample }
  end

  factory :customer_ask do
    tenant
  end

  factory :customer_requirement do
    customer_ask
    category { CUSTOMER_REQUIREMENT_CATEGORY.sample }
    status { CUSTOMER_REQUIREMENT_STATUS.sample }
    due_date { 1.month.from_now }
    description { "need voice calling" }
  end
end
