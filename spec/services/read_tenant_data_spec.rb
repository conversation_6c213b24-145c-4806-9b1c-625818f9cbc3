# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ReadTenantData, type: :model do
  before(:all) do
    Tenant.delete_all
    AccountDetail.delete_all
    AccountDetailHistory.delete_all
    Usage.delete_all
    UsageHistory.delete_all
  end

  let(:service) { ReadTenantData.new }

  context 'when data is not populated' do
    it 'populates the tenant data into the database' do
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: "qa/tenantUsage/#{Date.yesterday.year}-#{Date.yesterday.strftime("%B").upcase}/tenantUsage_#{Date.yesterday.strftime("%Y-%m-%d")}.csv",
          response_target: "tmp/onboarding.csv"
        )

      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: "qa/dailyUsage/#{Date.yesterday.year}-#{Date.yesterday.strftime("%B").upcase}/dailyUsage_#{Date.yesterday.strftime("%Y-%m-%d")}.csv",
          response_target: "tmp/daily-usage.csv"
        )

      # Simulate downloaded files
      File.open(ONBOARDING_SYSTEM_UPDATE_FILE_PATH, 'w') do |file|
        file.write File.read('spec/fixtures/files/onboarding-test.csv')
      end

      File.open(USAGE_SYSTEM_UPDATE_FILE_PATH, 'w') do |file|
        file.write File.read('spec/fixtures/files/usage-test.csv')
      end

      ReadTenantData.call

      expect(AccountDetail.count).to be 39
      expect(Usage.count).to be 29
    end

    it 'should populate data and store the required fields in matrix fields in the database' do
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: "qa/tenantUsage/#{Date.yesterday.year}-#{Date.yesterday.strftime("%B").upcase}/tenantUsage_#{Date.yesterday.strftime("%Y-%m-%d")}.csv",
          response_target: "tmp/onboarding.csv"
        )

      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: "qa/dailyUsage/#{Date.yesterday.year}-#{Date.yesterday.strftime("%B").upcase}/dailyUsage_#{Date.yesterday.strftime("%Y-%m-%d")}.csv",
          response_target: "tmp/daily-usage.csv"
        )

      File.open(ONBOARDING_SYSTEM_UPDATE_FILE_PATH, 'w') do |file|
        file.write File.read('spec/fixtures/files/onboarding-test.csv')
      end

      File.open(USAGE_SYSTEM_UPDATE_FILE_PATH, 'w') do |file|
        file.write File.read('spec/fixtures/files/usage-test.csv')
      end

      ReadTenantData.call
      expect(AccountDetail.count).to be 39
      expect(Usage.count).to be 29

      account_detail = AccountDetail.first
      usage = Usage.first

      expect(account_detail.metric_fields).to be_a(Hash)
      expect(usage.metric_fields).to be_a(Hash)
    end
  end

  context 'when data is already populated' do
    before(:each) do
      @count = AccountDetailHistory.count
    end

    it 'updates the tenant data into the database' do
      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: "qa/tenantUsage/#{Date.yesterday.year}-#{Date.yesterday.strftime("%B").upcase}/tenantUsage_#{Date.yesterday.strftime("%Y-%m-%d")}.csv",
          response_target: "tmp/onboarding.csv"
        )

      expect_any_instance_of(Aws::S3::Client)
        .to receive(:get_object)
        .with(
          bucket: S3_BUCKET_NAME,
          key: "qa/dailyUsage/#{Date.yesterday.year}-#{Date.yesterday.strftime("%B").upcase}/dailyUsage_#{Date.yesterday.strftime("%Y-%m-%d")}.csv",
          response_target: "tmp/daily-usage.csv"
        )

      File.open(ONBOARDING_SYSTEM_UPDATE_FILE_PATH, 'w') do |file|
        file.write File.read('spec/fixtures/files/onboarding-test.csv')
      end

      File.open(USAGE_SYSTEM_UPDATE_FILE_PATH, 'w') do |file|
        file.write File.read('spec/fixtures/files/usage-test.csv')
      end

      ReadTenantData.call

      expect(AccountDetailHistory.count).to be > @count
    end
  end
end
