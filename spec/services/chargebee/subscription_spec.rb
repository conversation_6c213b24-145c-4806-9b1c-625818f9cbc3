# frozen_string_literal = true
require 'rails_helper'
require 'webmock/rspec'

RSpec.describe Chargebee::Subscription do

  let(:subscription_id) { 'abc12345'}
  let(:response_body) { file_fixture('subscription_details.json').read }
  let(:chargebee_url) { "https://kylas-sandbox-test.chargebee.com/api/v2/subscriptions/#{subscription_id}" }

  describe '#get' do
    context 'when API call is succesfull' do
      before do
        stub_request(:get, chargebee_url).
        with(
          headers: {
            'Authorization'=> "Basic #{Base64.encode64(CHARGEBEE_API_KEY).gsub!("\n", '')}",
          }).
        to_return(status: 200, body: response_body, headers: {})
      end

      it 'should return success true and data' do
        response = Chargebee::Subscription.get(subscription_id)
        expect(response).to eq(JSON.parse(response_body))
        expect(response).not_to be_nil
      end
    end

    context 'when the API request fails' do
      before do
        stub_request(:get, chargebee_url).
        with(
          headers: {
            'Authorization'=> "Basic #{Base64.encode64(CHARGEBEE_API_KEY).gsub!("\n", '')}",
          }).
        to_return(status: 500)
      end

      it 'should return nil and log an error' do
        expect(Rails.logger).to receive(:error).with('Failed to retrieve subscription.')
        response = Chargebee::Subscription.get(subscription_id)
        expect(response).to eq(nil)
      end
    end

    context 'when an exception occurs during the API request' do
      before do
        stub_request(:get, chargebee_url)
        .with(
          headers: {
          'Authorization'=> "Basic #{Base64.encode64(CHARGEBEE_API_KEY).gsub!("\n", '')}",
          })
          .to_raise(StandardError.new('API request failed'))
      end

      it 'should return nil and log an error' do
        expect(Rails.logger).to receive(:error).with("Error occurred while retrieving subscription: API request failed")
        response = Chargebee::Subscription.get(subscription_id)
        expect(response).to eq(nil)
      end
    end
  end
end
