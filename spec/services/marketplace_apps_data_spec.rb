# frozen_string_literal: true

require 'rails_helper'
require 'net/http'

RSpec.describe MarketplaceAppsData, type: :service do
  describe "#get_marketplace_apps" do
    let(:response_body) { file_fixture('marketplace_apps_data.json').read }

    context "when API call is successful" do
      before do
        allow(Net::HTTP).to receive(:get).and_return(response_body)
      end
  
      it "should return an array of app names" do
        expect(MarketplaceAppsData.call).to eq(["Indiamart", "Kylas SMS", "Kylas Product Recommendations"])
      end
    end

    context "when API call fails" do
      before do
        allow(Net::HTTP).to receive(:get).and_raise(StandardError.new('API request failed'))
      end
      
      it 'should rescue and handles API call errors' do
        result = MarketplaceAppsData.call
        expect(result).to eq(nil)
      end
    end
  end
end
