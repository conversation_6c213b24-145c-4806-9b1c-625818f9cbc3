{"content": [{"id": "ed9b9062-6ca6-40c3-b447-ab249bbbdd40", "name": "Indiamart", "categories": ["lead_integration"], "summary": "This app helps you to automatically fetch leads generated in your Indiamart account and store it in Kylas CRM.", "developerName": "<PERSON><PERSON><PERSON>", "verified": true, "icon": "https://marketplace-assets.kylas.io/assets/app_ed9b9062-6ca6-40c3-b447-ab249bbbdd40/<EMAIL>"}, {"id": "877a72b9-7ace-4cc9-b7e0-b2025fd0dccd", "name": "<PERSON><PERSON><PERSON>", "categories": ["collaboration", "productivity"], "summary": "Kylas SMS app helps users in sending sms to the Leads or Contact via message service providers. ", "developerName": "<PERSON><PERSON><PERSON>", "verified": true, "icon": "https://marketplace-assets.kylas.io/assets/app_877a72b9-7ace-4cc9-b7e0-b2025fd0dccd/<EMAIL>"}, {"id": "178a4015-0baa-4e0b-abc8-ace3ebec1098", "name": "Kylas Product Recommendations", "categories": ["productivity"], "summary": "Kylas Product recommendation app enables users to search most relevant products based on the Lead or Deal requirement from the full list of products from Kylas.", "developerName": "<PERSON><PERSON><PERSON>", "verified": true, "icon": "https://marketplace-assets.kylas.io/assets/app_178a4015-0baa-4e0b-abc8-ace3ebec1098/MicrosoftTeams-image (15).png"}]}