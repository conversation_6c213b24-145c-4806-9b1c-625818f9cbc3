# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "UsersController", type: :request do
  let(:admin) { create(:admin) }
  before(:each) do
    sign_in_resource(admin)
  end
  describe "#destroy" do
    context 'When user with id do not exist' do
      it 'should flash notice, deactivate and redirect to list of users' do
        delete "/users/111111"
        expect(flash[:danger]).to eq('User does not exist')
        assert_redirected_to '/users'
      end
    end

    context 'When user with id exist' do
      let(:user) { create(:user) }
      let(:params) { { id: user.id } }
      it 'should flash notice, deactivate and redirect to list of users' do
        delete "/users/#{params[:id]}", params: params
        expect(flash[:success]).to eq('User Deactivated Successfully')
        expect(ActionMailer::Base.deliveries.last.subject).to eq("Account Deactivated")
        expect(ActionMailer::Base.deliveries.last.to.first).to eq(user.email)
        assert_redirected_to '/users'
      end
    end
  end

  describe "#activate" do
    context 'When user with id do not exist' do
      it 'should flash notice, deactivate and redirect to list of users' do
        patch "/users/111111/activate"
        expect(flash[:danger]).to eq('User does not exist')
        assert_redirected_to '/users'
      end
    end

    context 'When user with id exist' do
      let(:user) { create(:deactivated_user) }
      let(:params) { { id: user.id } }
      it 'should flash notice, activate and redirect to list of users' do
        patch "/users/#{params[:id]}/activate", params: params
        expect(flash[:success]).to eq('User Activated Successfully')
        expect(ActionMailer::Base.deliveries.last.subject).to eq("Account Activated")
        expect(ActionMailer::Base.deliveries.last.to.first).to eq(user.email)
        assert_redirected_to '/users'
      end
    end
  end

  describe "#resend_invitation" do
    context 'When user with id do not exist' do
      it 'should flash notice, deactivate and redirect to list of users' do
        post "/users/111111/resend-invitation"
        expect(flash[:danger]).to eq('User does not exist')
        assert_redirected_to '/users'
      end
    end

    context 'When user with email exist' do
      let(:params) { { user: { email: Faker::Internet.unique.email } } }
      before do
        User.invite!(email: params.dig(:user, :email), name: Faker::Name.name) do |invitee|
          @invitee = invitee
        end
      end
      it 'should resend invite' do
        post "/users/#{@invitee[:id]}/resend-invitation", params: params
        expect(ActionMailer::Base.deliveries.last.subject).to eq("Invitation instructions")
        expect(ActionMailer::Base.deliveries.last.to.first).to eq(params.dig(:user, :email))
        expect(flash[:success]).to eq('User invited successfully')
        assert_redirected_to '/users'
      end
    end
  end

  describe "#update" do
    let(:invitee) { create(:user) }
    before do
      invitee.invite!
    end
    context 'When user with id do not exist' do
      it 'should flash notice, deactivate and redirect to list of users' do
        patch "/users/111111"
        expect(flash[:danger]).to eq('User does not exist')
        assert_redirected_to '/users'
      end
    end

    context 'When User data are valid' do
      context 'when new email is different than old' do
        let(:params) { { user: { email: Faker::Internet.unique.email, name: Faker::Name.name } } } 
        it 'should deactivate the current user invite the user on new email' do
          patch "/users/#{invitee.id}", params: params
          expect(ActionMailer::Base.deliveries.last.subject).to eq("Invitation instructions")
          expect(ActionMailer::Base.deliveries.last.to.first).to eq(params.dig(:user, :email))
          expect(flash[:success]).to eq('Email Changed Successfully')
          assert_redirected_to '/users'
        end
      end

      context 'when new email is same as old' do
        let(:params) { { user: { email: invitee.email, name: Faker::Name.name } } }
        it 'should deactivate the current user invite the user on new email' do
          patch "/users/#{invitee.id}", params: params
          expect(flash[:success]).to eq('Name Changed Successfully')
          assert_redirected_to '/users'
        end
      end
    end

    context 'When user data is invalid' do
      context 'When previous email and new email is same' do
        let(:params) { { user: { email: 'badmail', name: invitee.name } } }
        before do
          invitee.accept_invitation!
        end
        it 'should update render edit form again with errors' do
          patch "/users/#{invitee.id}", params: params
          expect(response).to render_template(:edit)
        end
      end
    end
  end

  describe "#create" do
    let(:user) { create(:user) }
    context 'When User data are valid' do
      let(:params) { { user: { email: Faker::Internet.unique.email, name: Faker::Name.name } } }
      it 'should send invitation email' do
        post '/users', params: params
        expect(ActionMailer::Base.deliveries.last.subject).to eq("Invitation instructions")
        expect(ActionMailer::Base.deliveries.last.to.first).to eq(params.dig(:user, :email))
        expect(flash[:success]).to eq("User invited successfully")
        assert_redirected_to '/users'
      end
    end

    context 'When user data is invalid' do
      let(:params) { { user: { email: user.name, name: user.name } } }
      it 'should render a new form with unprocessable status' do
        post '/users', params: params
        expect(response).to render_template(:new)
      end
    end
  end

  describe "#index" do
    let(:count) { User.where(role: USER).limit(10).count }
    context 'When user is active' do
      it 'should render account listing screen with navbar' do
        get '/users/?page=1&items=10'
        assert_select 'table', 1
        assert_select 'tr' do |column|
          assert_select column, 'td', count * 4
        end
      end
    end

    context 'When User is deactivated' do
      let(:user) { create(:deactivated_user) }
      before do
        sign_out_resource(admin)
        sign_in_resource(user)
      end
      it 'should redirect user to sign in page and flash error message' do
        get '/'
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to new_user_session_path
      end
    end
  end

  describe '#new' do
    it 'should render new form to add user' do
      get '/users/new'
      assert_select 'form input', 3
    end
  end

  describe '#edit' do
    context 'When user with id do not exist' do
      it 'should flash notice, deactivate and redirect to list of users' do
        get "/users/111111/edit"
        expect(flash[:danger]).to eq('User does not exist')
        assert_redirected_to '/users'
      end
    end

    context "User with given id exists" do
      let(:user) { create(:user) }
      it 'should render new form to add user' do
        get "/users/#{user.id}/edit"
        assert_select 'form input', 4
      end
    end
  end

  describe '#list' do
    context 'When Admin is logged in' do
      before do
        @count = User.where(role: USER).count
      end
      it 'should render list of users' do
        get '/users/list.json'
        expect(JSON.parse(response.body).count).to eq(@count)
      end
    end

    context 'When Deactivated User is logged in' do
      let(:user) { create(:deactivated_user) }
      before do
        sign_out_resource(admin)
        sign_in_resource(user)
      end
      it 'should redirect to dashboard and flash an alert' do
        get '/users/new'
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to '/users/sign_in'
      end
    end

    context 'When User is logged in' do
      let(:user) { create(:user) }
      before do
        sign_out_resource(admin)
        sign_in_resource(user)
      end
      it 'should redirect to dashboard and flash an alert' do
        get '/users/new'
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to '/'
      end
    end
  end
end
