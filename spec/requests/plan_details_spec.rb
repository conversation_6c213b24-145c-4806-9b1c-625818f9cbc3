# frozen_string_literal: true

require 'rails_helper'
require 'webmock/rspec'

RSpec.describe "PlanDetails", type: :request do
  
  let(:user) { create(:user) }
  let(:deactivated_user) { create(:deactivated_user) }
  let(:tenant) { create(:tenant) }
  let(:account) { create(:account_detail, tenant: tenant) }
  let(:plan_detail) { create(:plan_detail, tenant: tenant) }
  let(:params) { { plan_detail: { ob_completion: Date.today } } }
  let(:invalid_params) { { plan_detail: { ob_completion: nil, tenant_id:nil } } }
  let(:chargebee_url) { "http://127.0.0.1:3000/subscription_details/abc12345" }
  let(:response_body) { file_fixture('subscription_details.json').read }
  
  before(:each) do
    sign_in_resource(user)
  end

  describe "#show" do
    context "when account details does not exist" do
      it "redirects to account details listing path" do
        get "/account-details/-1/plan-details"
        expect(response).to redirect_to(account_details_path)
        expect(flash[:notice]).to eq(I18n.t('account_details.not_found'))
      end
    end

    context 'when plan detail does not exist for a tenant' do
      it 'should redirect to account details page' do
        allow(PlanDetail).to receive(:find_by).and_return(nil)
        get "/account-details/#{account.id}/plan-details"
        expect(flash[:danger]).to eq('Plan detail does not exist')
      end
    end

    context "when plan detail exists" do
      before do
        plan_detail
        stub_request(:get, chargebee_url).
         with(
           headers: {
            'Authorization'=>CHARGEBEE_API_KEY,
           }).
         to_return(status: 200, body: response_body, headers: {})
      end

      it "should display the plan name and price" do
        get "/account-details/#{account.id}/plan-details"
        assert_response :success
        assert_select 'h3.plan-name'
        assert_select 'h5.price'
      end
  
      it "should display the plan status" do
        get "/account-details/#{account.id}/plan-details"
        assert_response :success
        assert_select 'h4'
      end
  
      it "should display the status and next renewal date" do
        get "/account-details/#{account.id}/plan-details"
        assert_response :success
        assert_select 'h5.sub-text'
      end
    end

    context "when plan details are unavailable from chargebee" do
      before do
        plan_detail
        stub_request(:get, chargebee_url).
         with(
           headers: {
            'Authorization'=>CHARGEBEE_API_KEY,
           }).
         to_return(status: 404, body: "", headers: {})
      end

      it "should return status code 404" do
        get "/account-details/#{account.id}/plan-details"
        expect(flash[:danger]).to eq("Plan detail does not exist")
      end
    end

    context 'When User is deactivated' do
      before do
        sign_in_resource(deactivated_user)
      end
      it 'should redirect user to sign in page and flash error message' do
        get "/account-details/#{account.id}/plan-details"
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to new_user_session_path
      end
    end
  end

  describe "#update" do
    before do
      plan_detail
    end
    context 'when plan detail exist' do
      it 'should save the data' do
        post "/account-details/#{account.id}/plan-details", params: params
        expect(flash[:success]).to eq('Successfully saved the data')
        expect(response.status).to eq(302)
      end
    end
    
    context "with invalid attributes" do
      it "should not update the plan detail" do
        allow_any_instance_of(PlanDetail).to receive(:update).and_return(false)
        post "/account-details/#{account.id}/plan-details", params: invalid_params
        expect(flash[:danger]).to eq('Error in saving the data')
        assert_redirected_to plan_details_account_detail_path
      end
    end
  end
end
