# frozen_string_literal: true

require 'rails_helper'
require 'webmock/rspec'

RSpec.describe "RgsInputs", type: :request do
  
  let(:user) { create(:user) }
  let(:deactivated_user) { create(:deactivated_user) }
  let(:tenant) { create(:tenant) }
  let(:account) { create(:account_detail, tenant: tenant, marketplace_apps_installed: ["Indiamart", "Kylas SMS", "Kylas Product Recommendations"]) }
  let(:rgs_input) { create(:rgs_input, tenant: tenant) }
  let(:marketplace_apps) { create(:marketplace_app, rgs_input: rgs_input) }
  let(:params) { { rgs_input: {
                  entities_attributes: { '0': { critical: true , expected_volume: '100', category: 'LEAD', frequency: 'MONTHLY' } },
                  marketplace_apps_attributes: { '0': { name: 'Zendesk', integrated: true } } } 
                  } 
                }
  let(:invalid_params) { { rgs_input: {
                  entities_attributes: { '0': { critical: nil , expected_volume: '100', category: 'LEAD', frequency: 'MONTHLY' } },
                  marketplace_apps_attributes: { '0': { name: 'WRONG NAME', integrated: true } } } 
                  } 
                }
  let(:response_body) { file_fixture('marketplace_apps_data.json').read }
  
  before(:each) do
    sign_in_resource(user)
  end

  before do
    stub_request(:get, "https://api.kylas.io/v1/marketplace/public/apps?page=0&size=1000").
      with(
        headers: {
      'Host'=>'api.kylas.io',
        }).
      to_return(status: 200, body: response_body, headers: {})
  end

  describe "#show" do
    context "when account details does not exist" do
      it "redirects to account_details_path" do
        get "/account-details/-1/rgs-inputs"
        expect(response).to redirect_to(account_details_path)
        expect(flash[:notice]).to eq(I18n.t('account_details.not_found'))
      end
    end

    context "when rgs input exists" do
      it "should display the page" do
        get "/account-details/#{account.id}/rgs-inputs"
        assert_response :success
      end

      it "should diplay all the neccassary fields" do
        get "/account-details/#{account.id}/rgs-inputs"
        assert_select 'table', 2
        assert_select 'tr', 6
        assert_select 'select', 5
        assert_response :success
      end
    end

    context 'When User is deactivated' do
      before do
        sign_in_resource(deactivated_user)
      end
      it 'should redirect user to sign in page and flash error message' do
        get "/account-details/#{account.id}/rgs-inputs"
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to new_user_session_path
      end
    end
  end

  describe "#update" do
    context "when the data passed is right" do
      it "should update the rgs input" do
        post "/account-details/#{account.id}/rgs-inputs", params: params
        expect(flash[:success]).to eq('Successfully saved the data')
        expect(response.status).to eq(302)
      end
    end

    context "when we pass the invalid data" do      
      it "should not update the rgs input" do
        post "/account-details/#{account.id}/rgs-inputs", params: invalid_params
        expect(flash[:danger]).to eq('Error in saving the data')
        expect(response.status).to eq(302) 
      end
    end

    context "when marketplace apps are present in account details" do
      before do        
        marketplace_apps
      end
      it 'creates new marketplace apps' do
        get "/account-details/#{account.id}/rgs-inputs"
        expect(rgs_input.marketplace_apps.count).to eq(4)
      end
    end
  end
end
