# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "CustomerAsks", type: :request do
  let(:user) { create(:user) }
  let(:tenant) { create(:tenant) }
  let(:account_details) { create(:account_detail, tenant: tenant) }
  let(:customer_ask) { create(:customer_ask, tenant: tenant) }
  let(:customer_requirement) { create(:customer_requirement, status: 'ACCEPTED', category: 'COMPANY', description: 'Need payment integration.', due_date: Date.today, customer_ask_id: customer_ask.id) }
  let(:params) do
    {
      customer_requirement: {
        status: 'ACCEPTED',
        category: 'LEAD',
        description: 'Need pipeline fo customer onboarding.',
        due_date: Date.today
      }
    }
  end
  let(:update_params) do
    {
      customer_requirement: {
        status: 'ACCEPTED',
        category: 'LEAD',
        description: 'Need pipeline fo customer onboarding.',
        due_date: Date.today,
        id: customer_requirement.id
      }
    }
  end
  let(:invalid_update_params) do
    {
      customer_requirement: {
        status: 'BAD_VALUE',
        category: 'LEAD',
        description: 'Need pipeline fo customer onboarding.',
        due_date: Date.today,
        id: customer_requirement.id
      }
    }
  end
  let(:destroy_params) do
    {
      customer_requirement: {
        id: customer_requirement.id
      }
    }
  end
  let(:invalid_params) do
    {
      customer_requirement: {
        status: 'BAD_VALUE',
        category: 'LEAD',
        description: 'Need pipeline fo customer onboarding.',
        due_date: Date.today
      }
    }
  end
  let(:invalid_destroy_params) do
    {
      customer_requirement: {
        id: 1111
      }
    }
  end

  before(:each) do
    sign_in_resource(user)
  end

  describe '#show' do
    context 'when account details does not exist' do
      it 'redirects to account_details_path' do
        get '/account-details/-1/customer-asks'
        expect(response).to redirect_to(account_details_path)
        expect(flash[:notice]).to eq('Account Not Found')
      end
    end

    context 'when user is not signed in' do
      before do
        sign_out_resource(user)
      end
      it 'redirects to new_user_session_path' do
        get '/account-details'
        expect(flash[:alert]).to eq('You need to sign in or sign up before continuing.')
        assert_redirected_to redirect_to(new_user_session_path)
      end
    end

    context 'when customer ask exists' do
      it 'should display the page' do
        get "/account-details/#{account_details.id}/customer-asks"
        assert_response :success
      end

      it 'should display all the neccassary fields' do
        get "/account-details/#{account_details.id}/customer-asks"
        assert_select 'label', 'Description *:', 1
        assert_select 'label', 'For *:', 1
        assert_select 'label', 'Due Date *:', 1
        assert_response :success
      end
    end

    context 'When User is deactivated' do
      before do
        user = create(:deactivated_user)
        sign_in_resource(user)
      end
      it 'should redirect user to sign in page and flash error message' do
        get "/account-details/#{account_details.id}/customer-asks"
        expect(flash[:danger]).to eq('You do not have permission to log into the application')
        assert_redirected_to new_user_session_path
      end
    end
  end

  describe '#update' do
    context 'when the data passed is right' do
      it 'should update the customer requirement' do
        post "/account-details/#{account_details.id}/customer-asks/edit", params: update_params
        expect(flash[:success]).to eq('Requirement Updated Successfully')
        expect(response.status).to eq(302)
      end
    end

    context 'when we pass the invalid data' do
      it 'should not update the customer requirement' do
        post "/account-details/#{account_details.id}/customer-asks/edit", params: invalid_update_params
        expect(flash[:danger]).to eq('Status BAD_VALUE is not a valid status')
        expect(response.status).to eq(302)
      end
    end
  end
  describe '#create' do
    context 'when the data passed is right' do
      it 'should create the customer requirement' do
        post "/account-details/#{account_details.id}/customer-asks/add", params: params
        expect(flash[:success]).to eq('Requirement Created Successfully')
        expect(response.status).to eq(302)
      end
    end

    context 'when we pass the invalid data' do
      it 'should not create the customer requirement' do
        post "/account-details/#{account_details.id}/customer-asks/add", params: invalid_params
        expect(flash[:danger]).to eq('Status BAD_VALUE is not a valid status')
        expect(response.status).to eq(302)
      end
    end
  end

  describe '#destroy' do
    context 'when the data passed is right' do
      it 'should destroy the customer requirement' do
        post "/account-details/#{account_details.id}/customer-asks/remove", params: destroy_params
        expect(flash[:success]).to eq('Requirement Destroyed Successfully')
        expect(response.status).to eq(302)
      end
    end

    context 'when we pass the invalid data' do
      it 'should not destroy the customer requirement' do
        post "/account-details/#{account_details.id}/customer-asks/remove", params: invalid_destroy_params
        expect(flash[:danger]).to eq('Requirement Doesnt Exist')
        expect(response.status).to eq(302)
      end
    end
  end
end
