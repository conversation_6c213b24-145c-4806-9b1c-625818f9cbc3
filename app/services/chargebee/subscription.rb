# frozen_string_literal: true

require 'net/http'

module Chargebee
  class Subscription
    def self.get(subscription_id)
      api_key = CHARGEBEE_API_KEY
      uri = URI("#{CHARGEBEE_URL}/#{subscription_id}")

      request = Net::HTTP::Get.new(uri)
      request["Authorization"] = "Basic #{Base64.encode64(api_key).gsub!("\n", '')}"

      begin
        http = Net::HTTP.new(uri.hostname, uri.port)
        http.use_ssl = true
        response = http.request(request)

        if response.is_a?(Net::HTTPSuccess) && response.code == '200'
          return JSON.parse(response.body)
        else
          error_message = I18n.t('chargebee.failed_to_retrieve')
          Rails.logger.error(error_message)
          return nil
        end
      rescue StandardError, Timeout::Error, Errno::ECONNREFUSED, JSON::ParserError => e
        Rails.logger.error("#{I18n.t('chargebee.error_message')} #{e.message}")
        return nil
      end
    end
  end
end
