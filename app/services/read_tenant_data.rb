# frozen_string_literal: true

class ReadTenantData < ApplicationService
  require 'aws-sdk-s3'
  require 'csv'

  def initialize
    super
    Aws.config.update({
      endpoint: AWS_END_POINT,
      region: AWS_REGION,
      credentials: Aws::Credentials.new(AWS_ACCESS_KEY, AWS_SECRET_KEY)
    })
  end

  def call
    s3_client = Aws::S3::Client.new
    begin
      s3_client.get_object(
        bucket: S3_BUCKET_NAME,
        key: onboarding_file_path(Date.yesterday),
        response_target: ONBOARDING_SYSTEM_UPDATE_FILE_PATH
      )
      CSV.foreach(ONBOARDING_SYSTEM_UPDATE_FILE_PATH, headers: true) do |row|
        begin
          row_hash = row.to_h
          next if row_hash['planName'] == EMBARK

          row_hash.transform_keys!(&:underscore)

          tenant = read_tenant_detail(row_hash)
          read_account_detail(row_hash, tenant)
        rescue StandardError => e
          Rails.logger.info  "Onboarding row failed: #{e.message} | Row: #{row.to_h.inspect}"
          next
        end
      end

      s3_client.get_object(
        bucket: S3_BUCKET_NAME,
        key: usage_file_path(Date.yesterday),
        response_target: USAGE_SYSTEM_UPDATE_FILE_PATH
      )
      CSV.foreach(USAGE_SYSTEM_UPDATE_FILE_PATH, headers: true) do |row|
        begin
          row_hash = row.to_h
          next if row_hash['planName'] == EMBARK

          row_hash.transform_keys!(&:underscore)

          read_usage(row_hash)
        rescue StandardError => e
          Rails.logger.info  "Usage row failed: #{e.message} | Row: #{row.to_h.inspect}"
          next
        end
      end

      Rails.logger.info  "Tenants details updated successfully"
    rescue StandardError => e
      Rails.logger.info  "S3 or overall failure: #{e.message}"
    end
  end

  def onboarding_file_path(date = Date.today)
    year = date.year();
    month = date.strftime("%B").upcase
    folder_name = "#{year}-#{month}"
    formatted_date = date.strftime("%Y-%m-%d")
    file_name = "tenantUsage_#{formatted_date}.csv"
    Rails.env == 'production' ? File.join("tenantUsage", folder_name, file_name) : File.join("qa/tenantUsage", folder_name, file_name)
  end

  def usage_file_path(date = Date.today)
    year = date.year();
    month = date.strftime("%B").upcase
    folder_name = "#{year}-#{month}"
    formatted_date = date.strftime("%Y-%m-%d")
    file_name = "dailyUsage_#{formatted_date}.csv"
    Rails.env == 'production' ? File.join("dailyUsage", folder_name, file_name) : File.join("qa/dailyUsage", folder_name, file_name)
  end

  def read_tenant_detail(row)
    tenant = Tenant.find_or_initialize_by(kylas_tenant_id: row['tenant_id'])
    if tenant.persisted?
      tenant.update!(name: row['tenant_name'], system_updated_at: Time.now)
    else
      tenant.save!(name: row['tenant_name'], kylas_tenant_id: row['tenant_id'], system_updated_at: Time.now)
    end
    tenant
  end

  def read_account_detail(row, tenant)
    return unless tenant.present?
    account_detail_row = {}
    account_detail_row['email'] = row.delete 'tenant_user_email'
    account_detail_row['industry'] = row.delete 'tenant_industry'
    account_detail_row['start_date'] = row.delete 'signed_up_at'
    account_detail_row['company'] = row.delete 'company'
    account_detail_row['mobile'] = row.delete 'mobile'
    account_detail_row['marketplace_apps_installed'] = row['name_of_marketplace_apps_installed'].nil? ? [] : row['name_of_marketplace_apps_installed'].split(',')
    account_detail_row['tenant_name'] = row.delete('tenant_name')
    account_detail_row['kylas_tenant_id'] = row.delete('tenant_id')
    account_detail_row['tenant_id'] = tenant.id
    account_detail_row['plan_name'] = row.delete 'plan_name'
    account_detail_row['account_manager_id'] = row.delete('account_manager_id')
    account_detail_row['support_executive_id'] = row.delete('supportive_executive_id')
    account_detail_row['account_settings_completed'] = row.delete('account_settings_completed')
    account_detail_row['status'] = row.delete('status')
    account_detail_row['dau_true'] = row.delete('dau_true')
    account_detail_row['dau_false'] = row.delete('dau_false')
    account_detail_row['subscription_id'] = row.delete('subscription_id')
    account_detail_row['last_updated_at'] = row.delete('usage_published_date')
    account_detail_row['metric_fields'] = row
    row.delete('name_of_marketplace_apps_installed')
    row.delete('customer_id')
    account_detail = tenant.account_detail
    if account_detail
      account_detail.update!(**account_detail_row)
    else
      AccountDetail.create!(**account_detail_row)
    end
    account_detail_row.delete('last_updated_at')
    AccountDetailHistory.create!(**account_detail_row)
    Rails.logger.info  "Tenant #{account_detail_row['kylas_tenant_id']} | Account Details saved successfully"
  end

  def read_usage(row)
    tenant = Tenant.find_by(kylas_tenant_id: row['tenant_id'])
    unless tenant.present?
      Rails.logger.info "Tenant #{row['tenant_id']} | Tenant Not found"
      return
    end
    usage_detail_row = {}
    usage_detail_row['name_of_marketplace_apps_installed'] = row['name_of_marketplace_apps_installed'].nil? ? [] : row['name_of_marketplace_apps_installed'].split(',')
    usage_detail_row['all_phone_numbers'] = row['all_phone_numbers'].nil? ? [] : row['all_phone_numbers'].split(',')
    usage_detail_row['tenant_name'] = row.delete 'tenant_name'
    usage_detail_row['kylas_tenant_id'] = row.delete('tenant_id')
    usage_detail_row['tenant_id'] = tenant.id
    usage_detail_row['user_id'] = row.delete('user_id')
    usage_detail_row['full_name'] = row.delete('full_name')
    usage_detail_row['email'] = row.delete('email')
    usage_detail_row['plan_name'] = row.delete('plan_name')
    usage_detail_row['status'] = row.delete('status')
    usage_detail_row['last_login_at'] = row.delete('last_login_at')
    usage_detail_row['created_at'] = row.delete('created_at')
    usage_detail_row['updated_at'] = row.delete('updated_at')
    usage_detail_row['deactivated_at'] = row.delete('deactivated_at')
    usage_detail_row['active'] = row.delete('active')
    usage_detail_row['verified'] = row.delete('verified')
    usage_detail_row['dau'] = row.delete('dau')
    usage_detail_row['primary_phone_number'] = row.delete('primary_phone_number')
    usage_detail_row['all_phone_numbers'] = row.delete('all_phone_numbers')
    row.delete('name_of_marketplace_apps_installed')
    usage_detail_row['metric_fields'] = row

    usage = Usage.find_by(user_id: usage_detail_row['user_id'])
    if usage
      usage.update!(**usage_detail_row)
    else
      Usage.create!(**usage_detail_row)
    end
    UsageHistory.create!(**usage_detail_row)
    Rails.logger.info  "Tenant #{usage_detail_row['kylas_tenant_id']} | Usage Details saved successfully"
  end
end
