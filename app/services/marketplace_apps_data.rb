# frozen_string_literal: true

class MarketplaceAppsData < ApplicationService
  
  require 'net/http'

  def call
    get_marketplace_apps
  end
  
  def get_marketplace_apps
    begin
      uri = URI('https://api.kylas.io/v1/marketplace/public/apps?page=0&size=1000')
      response = Net::HTTP.get(uri)
      content_data = JSON.parse(response)
      content_data["content"].collect { |obj| obj["name"] }
    rescue StandardError, Timeout::Error, Errno::ECONNREFUSED, JSON::ParserError => e
      Rails.logger.error e
      return nil
    end
  end
end
