class ReportGenerationJob < ApplicationJob
  queue_as :default

  def perform(report)
    result = report.report_results.create!(
      generated_at: Time.current,
      generation_status: 'processing'
    )

    begin
      # Generate the report data based on report configuration
      data = generate_report_data(report)
      chart_data = generate_chart_data(report, data)
      summary_stats = generate_summary_stats(report, data)

      result.mark_as_completed!(data, chart_data, summary_stats)
      
      # Update report's last generated timestamp
      report.update!(last_generated_at: Time.current)
      
      # Schedule next generation if report is scheduled
      report.schedule_next_generation! if report.is_scheduled?
      
    rescue StandardError => e
      result.mark_as_failed!(e.message)
      Rails.logger.error "Report generation failed for report #{report.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end

  private

  def generate_report_data(report)
    case report.data_source
    when 'onboarding'
      generate_onboarding_data(report)
    when 'usage'
      generate_usage_data(report)
    when 'both'
      generate_combined_data(report)
    else
      []
    end
  end

  def generate_onboarding_data(report)
    # Start with account_details as the base for onboarding data
    query = AccountDetail.includes(:tenant)
    
    # Apply filters
    report.report_filters.active.each do |filter|
      query = filter.apply_filter(query, 'onboarding')
    end

    # Select metrics based on report configuration
    metrics = report.metrics.presence || Report::ONBOARDING_METRICS
    
    query.limit(1000).map do |account|
      row = {}
      
      metrics.each do |metric|
        case metric
        when 'total_users'
          row[metric] = account.metric_fields&.dig('total_users') || 0
        when 'active_users'
          row[metric] = account.metric_fields&.dig('active_users') || 0
        when 'inactive_users'
          row[metric] = account.metric_fields&.dig('inactive_users') || 0
        when 'admin_users'
          row[metric] = account.metric_fields&.dig('admin_users') || 0
        when 'total_marketplace_apps'
          row[metric] = account.metric_fields&.dig('total_marketplace_apps') || 0
        when 'active_marketplace_apps'
          row[metric] = account.metric_fields&.dig('active_marketplace_apps') || 0
        when 'subscription_plan'
          row[metric] = account.metric_fields&.dig('subscription_plan') || 'Unknown'
        when 'subscription_status'
          row[metric] = account.metric_fields&.dig('subscription_status') || 'Unknown'
        when 'account_created_date'
          row[metric] = account.created_at.strftime('%Y-%m-%d')
        when 'last_login_date'
          row[metric] = account.metric_fields&.dig('last_login_date') || 'Never'
        when 'storage_used'
          row[metric] = account.metric_fields&.dig('storage_used') || 0
        when 'api_calls_made'
          row[metric] = account.metric_fields&.dig('api_calls_made') || 0
        else
          # Try to get from metric_fields JSON
          row[metric] = account.metric_fields&.dig(metric) || 0
        end
      end
      
      # Always include tenant info
      row['tenant_id'] = account.tenant_id
      row['tenant_name'] = account.tenant&.name || 'Unknown'
      
      row
    end
  end

  def generate_usage_data(report)
    # Start with usages as the base for usage data
    query = Usage.includes(:tenant)
    
    # Apply filters
    report.report_filters.active.each do |filter|
      query = filter.apply_filter(query, 'usage')
    end

    # Select metrics based on report configuration
    metrics = report.metrics.presence || Report::USAGE_METRICS
    
    query.limit(1000).map do |usage|
      row = {}
      
      metrics.each do |metric|
        case metric
        when 'leads_created'
          row[metric] = usage.metric_fields&.dig('leads_created') || 0
        when 'deals_created'
          row[metric] = usage.metric_fields&.dig('deals_created') || 0
        when 'contacts_created'
          row[metric] = usage.metric_fields&.dig('contacts_created') || 0
        when 'calls_made'
          row[metric] = usage.metric_fields&.dig('calls_made') || 0
        when 'emails_sent'
          row[metric] = usage.metric_fields&.dig('emails_sent') || 0
        when 'tasks_completed'
          row[metric] = usage.metric_fields&.dig('tasks_completed') || 0
        when 'login_count'
          row[metric] = usage.metric_fields&.dig('login_count') || 0
        when 'active_time_minutes'
          row[metric] = usage.metric_fields&.dig('active_time_minutes') || 0
        when 'features_used'
          row[metric] = usage.metric_fields&.dig('features_used') || []
        when 'integrations_used'
          row[metric] = usage.metric_fields&.dig('integrations_used') || []
        when 'daily_active_users'
          row[metric] = usage.metric_fields&.dig('daily_active_users') || 0
        when 'weekly_active_users'
          row[metric] = usage.metric_fields&.dig('weekly_active_users') || 0
        when 'monthly_active_users'
          row[metric] = usage.metric_fields&.dig('monthly_active_users') || 0
        else
          # Try to get from metric_fields JSON
          row[metric] = usage.metric_fields&.dig(metric) || 0
        end
      end
      
      # Always include tenant and date info
      row['tenant_id'] = usage.tenant_id
      row['tenant_name'] = usage.tenant&.name || 'Unknown'
      row['date'] = usage.date.strftime('%Y-%m-%d')
      
      row
    end
  end

  def generate_combined_data(report)
    # For combined reports, we'll create a summary that joins both data sources
    onboarding_data = generate_onboarding_data(report)
    usage_data = generate_usage_data(report)
    
    # Group usage data by tenant for aggregation
    usage_by_tenant = usage_data.group_by { |row| row['tenant_id'] }
    
    onboarding_data.map do |onboarding_row|
      tenant_id = onboarding_row['tenant_id']
      tenant_usage = usage_by_tenant[tenant_id] || []
      
      # Aggregate usage metrics for this tenant
      combined_row = onboarding_row.dup
      
      if tenant_usage.any?
        # Add aggregated usage metrics
        combined_row['total_leads_created'] = tenant_usage.sum { |u| u['leads_created'].to_i }
        combined_row['total_deals_created'] = tenant_usage.sum { |u| u['deals_created'].to_i }
        combined_row['total_contacts_created'] = tenant_usage.sum { |u| u['contacts_created'].to_i }
        combined_row['avg_daily_active_users'] = tenant_usage.sum { |u| u['daily_active_users'].to_i } / tenant_usage.size.to_f
        combined_row['total_login_count'] = tenant_usage.sum { |u| u['login_count'].to_i }
      end
      
      combined_row
    end
  end

  def generate_chart_data(report, data)
    return {} if data.empty?

    case report.report_type
    when 'onboarding'
      generate_onboarding_chart_data(data)
    when 'usage'
      generate_usage_chart_data(data)
    when 'comparison'
      generate_comparison_chart_data(data)
    else
      generate_default_chart_data(data)
    end
  end

  def generate_onboarding_chart_data(data)
    # Create a simple bar chart showing tenant distribution
    tenant_counts = data.group_by { |row| row['tenant_name'] }.transform_values(&:count)
    
    {
      type: 'bar',
      data: {
        labels: tenant_counts.keys,
        datasets: [{
          label: 'Accounts per Tenant',
          data: tenant_counts.values,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      }
    }
  end

  def generate_usage_chart_data(data)
    # Create a line chart showing usage over time
    usage_by_date = data.group_by { |row| row['date'] }
                        .transform_values { |rows| rows.sum { |r| r['leads_created'].to_i } }
                        .sort_by { |date, _| date }
    
    {
      type: 'line',
      data: {
        labels: usage_by_date.keys,
        datasets: [{
          label: 'Leads Created Over Time',
          data: usage_by_date.values,
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 2,
          fill: false
        }]
      }
    }
  end

  def generate_comparison_chart_data(data)
    # Create a comparison chart based on the first numeric metric found
    numeric_metrics = data.first&.select { |k, v| v.is_a?(Numeric) }&.keys || []
    return {} if numeric_metrics.empty?

    metric = numeric_metrics.first
    tenant_values = data.group_by { |row| row['tenant_name'] }
                        .transform_values { |rows| rows.sum { |r| r[metric].to_f } }
    
    {
      type: 'bar',
      data: {
        labels: tenant_values.keys,
        datasets: [{
          label: metric.humanize,
          data: tenant_values.values,
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }]
      }
    }
  end

  def generate_default_chart_data(data)
    # Default to a simple count chart
    {
      type: 'pie',
      data: {
        labels: ['Total Records'],
        datasets: [{
          data: [data.size],
          backgroundColor: ['rgba(153, 102, 255, 0.6)'],
          borderColor: ['rgba(153, 102, 255, 1)'],
          borderWidth: 1
        }]
      }
    }
  end

  def generate_summary_stats(report, data)
    return {} if data.empty?

    stats = {
      'total_records' => data.size,
      'generated_at' => Time.current.strftime('%Y-%m-%d %H:%M:%S')
    }

    # Add specific stats based on data content
    if data.first.is_a?(Hash)
      numeric_fields = data.first.select { |k, v| v.is_a?(Numeric) }.keys
      
      numeric_fields.each do |field|
        values = data.map { |row| row[field].to_f }.compact
        next if values.empty?
        
        stats["#{field}_total"] = values.sum
        stats["#{field}_average"] = (values.sum / values.size.to_f).round(2)
        stats["#{field}_max"] = values.max
        stats["#{field}_min"] = values.min
      end
    end

    stats
  end
end
