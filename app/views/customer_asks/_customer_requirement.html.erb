<div class="modal" id='<%= modal_id %>' tabindex="-1" role="dialog">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"><%= t('customer_asks.customer_requirement.title') %></h4>
        <button type="button" class="btn btn-lg" data-dismiss="modal" aria-label="Close">
          <span class="csk-modal-close" aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body csk-modal-body">
        <%= form_for current_requirement, url: url, method: :post do |f| %> 
          <%= f.hidden_field :id %>
          <%= f.label :category, t('customer_asks.customer_requirement.for_label'), class: 'mt-3 category-label' %>
          <%= f.select :category, options_for_select(CUSTOMER_REQUIREMENT_CATEGORY.map { |option| [option.titleize, option] }, f.object.category), { include_blank: t('customer_asks.customer_requirement.choose') }, 
          class: 'form-select form-select-md category', required: true %>
          <%= f.label :status, t('customer_asks.customer_requirement.status_label'), class: 'mt-3 status-label' %>
          <%= f.select :status, options_for_select(CUSTOMER_REQUIREMENT_STATUS.map { |option| [option.titleize, option] }, f.object.status), { include_blank: t('customer_asks.customer_requirement.choose') }, 
          class: 'form-select form-select-md status', required: true %>
          <%= f.label :due_date, t('customer_asks.customer_requirement.due_date_label'), class: 'mt-3 due-date-label' %>
          <%= f.date_field :due_date, placeholder: t('customer_asks.customer_requirement.placeholder_due_date'), class:"form-control due-date" %>
          <%= f.label :description, t('customer_asks.customer_requirement.description_label'), class: 'mt-3 description-label' %>
          <%= f.text_area :description, rows: 6, class:'form-control form-control-text', placeholder: t('customer_asks.customer_requirement.placeholder_description'), required: true %>
      </div>
      <div class="modal-footer">
        <%= f.submit t('customer_asks.customer_requirement.add'), data: {disable_with: t('customer_asks.customer_requirement.adding')}, class: "btn save-btn" %>
        <button type="button" class="btn btn-outline-primary" data-dismiss="modal"><%= t('customer_asks.customer_requirement.cancel') %></button>
      </div>
        <% end %>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    $('.status').each((i,e)=>{
      if ($(e).val() == 'NOT_DOABLE') {
        $($(e).parent()[0]).find('.due-date').hide()
        $($(e).parent()[0]).find('.due-date-label').hide()
      }
    })
  })
  $('.status').click((e)=>{
    if (e.target.value == 'NOT_DOABLE') {
      $('.due-date').hide()
      $('.due-date-label').hide()
    }
    else {
      $('.due-date').show()
      $('.due-date-label').show()
    }
  })
</script>
