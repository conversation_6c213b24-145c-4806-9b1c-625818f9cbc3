<%= render layout:'account_details/shared/tabs_layout', locals: { account: @account_detail, tenant: @customer_ask.tenant } do %>
  <div class="header-text" >
    <%= t('customer_asks.header_text') %>
    <button type="button" class="btn btn-link" data-toggle="modal" data-target="#new">
      <%= t('customer_asks.customer_requirement.add_requirement') %>
    </button>
  </div>
  <% if @customer_requirements.count > 0 %>
    <div class="csk-table-container" >
      <table class="requirements-table table mt-3" >
        <thead>
          <tr class="d-flex">
            <th class="w-5" scope="col"><%= t('table.heading.id') %></th>
            <th class="w-10" scope="col"><%= t('table.heading.category') %></th>
            <th class="w-10" scope="col"><%= t('table.heading.status') %></th>
            <th class="w-65" scope="col"><%= t('table.heading.description') %></th>
            <th class="w-10" scope="col"><%= t('table.heading.actions') %></th>
          </tr>
        </thead>
        <tbody>
          <% @customer_requirements.map do |requirement| %>
            <tr class="d-flex">
              <td class="w-5" ><%= requirement.id %></td>
              <td class="w-10" ><%= requirement.category&.titleize %></td>
              <td class="w-10" ><%= requirement.status&.titleize %></td>
              <td class="w-65" ><div class="desc-container" ><%= requirement.description.truncate(200) %><div></td>
              <td class="w-10" >
                <div class="csk-btn-grp btn-grp">
                  <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#<%=requirement.id%>">
                    <i class="fa fa-edit" aria-hidden="true"></i>
                  </button>
                  <%= button_to  customer_asks_remove_account_detail_path, method: :post, class: 'btn btn-danger', params: { customer_requirement: { id: requirement.id } } do %>
                    <i class="fa fa-trash" aria-hidden="true"></i>
                  <% end %>
                  <%= render 'customer_requirement', current_requirement: requirement , url: customer_asks_edit_account_detail_url, modal_id: requirement.id %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
    <div class="pagination" >
      <%= render 'shared/pagination', pagy: @pagy, path: method(:customer_asks_account_detail_path) %>
    </div>
  <% end %>
  <%= render 'customer_requirement', current_requirement: CustomerRequirement.new(customer_ask_id: @customer_ask.id), url: customer_asks_add_account_detail_url, modal_id: "new" %>
<% end %>
