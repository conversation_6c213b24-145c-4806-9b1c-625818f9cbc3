<div class="d-flex flex-row-reverse mt-3" style="padding:10px">
  <%= link_to t('form.button.add_user'), new_user_path, class: "btn btn-primary btn-md" %>
</div>
<div class="data-container">
  <% if @users.count > 0 %>
  <div style="overflow:scroll;padding:10px;height:92%">
    <table class="table mt-3">
      <thead>
        <tr class="d-flex">
          <th class="w-25" scope="col"><%= t('table.heading.name') %></th>
          <th class="w-25" scope="col"><%= t('table.heading.email') %></th>
          <th class="w-25" scope="col"><%= t('table.heading.invitation_status') %></th>
          <th class="w-25" scope="col"><%= t('table.heading.actions') %></th>
        </tr>
      </thead>
      <tbody>
        <% @users.map do |user| %>
          <tr class="d-flex">
            <td class="w-25" ><%= user.name %></td>
            <td class="w-25" ><%= user.email %></td>
            <td class="w-25" ><%= user.invitation_accepted? ? 'Accepted' : 'Not Accepted' %></td>
            <td class="w-25" >
              <div class="d-flex">
                <% if user.deactivated %>
                  <%= button_to t('table.button.activate'), activate_user_path(user), method: :patch, class: "btn btn-success btn-md m-1" %>
                <% else %>
                  <%= button_to t('table.button.deactivate'), user_url(user), method: :delete, class: "btn btn-danger btn-md m-1" %>
                <% end %>
                <% unless user.invitation_accepted? %>
                  <%= button_to t('table.button.resend_invitation'), resend_invitation_user_url(user), method: :post, params:{ user: { email: user.email } }, class: "btn btn-primary btn-md m-1" %>
                <% end %>
                <%= button_to t('table.button.edit'), edit_user_url(user), method: :get, class: "btn btn-primary btn-md m-1" %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    </div>
    <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;padding:10px" >
      <%= render 'shared/pagination', pagy: @pagy, path: method(:users_path) %>
    </div>
  <% end %>
</div>
