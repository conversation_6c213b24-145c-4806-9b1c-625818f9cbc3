<h2><%= t "devise.invitations.new.header" %></h2>

<%= form_for(resource, as: resource_name, url: invitation_path(resource_name), html: { method: :post }) do |f| %>
  <%= render "users/shared/error_messages", resource: resource %>

  <% resource.class.invite_key_fields.each do |field| -%>
    <div class="field">
      <%= f.label field %><br/>
      <%= f.text_field field %>
    </div>
  <% end -%>

  <div class="actions">
    <%= f.submit t("devise.invitations.new.submit_button") %>
  </div>
<% end %>
