<div class='authentication-layout'>
  <div class='kylas-background'></div>
  <div class='content-wrapper'>
    <div class='main-content'>
      <div class='authentication-form-wrapper'>
        <img alt='logo' class='logo' src='logo.png'/>
        <h2 class="app-heading" ><%= t('app.title') %></h2>
        <h2><%= t "devise.invitations.edit.header" %></h2>
        <p class='message last-child'><%= t('password.set_your_password') %></p>
        <%= render 'users/shared/flash' %>
        <%= form_for(resource, as: resource_name, url: invitation_path(resource_name), html: { method: :put }) do |f| %>
          <%= f.hidden_field :invitation_token, readonly: true %>
          <div class='form-group'>
            <%= f.label t('form.field.password'), :input_html => { :class => 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='input-group flex'>
                <div class='validate password-box'>
                  <%= f.password_field :password, autocomplete: 'current-password', placeholder: t('form.field.password'), id: 'password', class: 'form-control form-control--password' %>
                </div>
                <div class="input-group-append">
                  <span toggle=".form-control--password#password" class="input-group-text toggle-password fa fa-fw fa-eye-slash field-icon pe-4 cursor-pointer" style='background-color: white; border-left: none; line-height: 1.5rem'>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:password].any? %>
            <span class='error'>
              <%= t('form.field.password') %>
              <%= resource.errors.messages[:password][0].downcase %>
            </span>
          <% end %>
          <div class='form-group'>
            <%= f.label t('form.field.confirm_password'), :input_html => { :class => 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='input-group flex' id='password'>
                <div class='validate password-box'>
                  <%= f.password_field :password_confirmation, autocomplete: 'current-password', placeholder: t('form.field.confirm_password'), id: 'input_reTypePassword', class: 'form-control form-control--password' %>
                </div>
                <div class="input-group-append">
                  <span toggle=".form-control--password#input_reTypePassword" class="input-group-text toggle-password fa fa-fw fa-eye-slash field-icon pe-4 cursor-pointer" style='background-color: white; border-left: none; line-height: 1.5rem'>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:password_confirmation,].any? %>
            <span class='error'>
              <%= t('app.confirm_password') %>
              <%= resource.errors.messages[:password_confirmation][0].downcase %>
            </span>
          <% end %>
          <div class='actions'>
            <%= f.submit t('form.button.set_password'), class: 'btn btn-md w-100 mb-3 mt-auto btn-primary' %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function(){
    $(".toggle-password").click(function() {
      $(this).toggleClass("fa-eye-slash fa-eye");
      var input = $($(this).attr("toggle"));
      if (input.attr("type") == "password") {
        input.attr("type", "text");
      } else {
        input.attr("type", "password");
      }
    });
  });
</script>
