<%= csrf_meta_tags %>
<div class='authentication-layout'>
  <div class='kylas-background'></div>
  <div class='content-wrapper'>
    <div class='main-content'>
      <div class='authentication-form-wrapper'>
        <%= image_tag 'logo.png', class: 'logo', alt: 'logo' %>
        <h2 class="app-heading" ><%= t('app.title') %></h2>
        <h2> <%= t('notice.sign_in_your_account') %> </h2>
        <%= image_tag 'sign-in.svg', class: 'sign-in__placeholder', alt: 'sign in placeholder' %>
        <p class='sign-in__mobile-help-text'> <%= t('app.for_better_user_experience') %> </p>
        <%= render 'users/shared/flash' %>
        <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: {id: 'signInForm'}) do |f| %>
          <div class='form-group'>
            <%= f.label t('form.field.email'), input_html: { class: 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='form-group' id='email'>
                <div class='validate'>
                  <%= f.email_field :email, autocomplete: 'off', placeholder: t('form.field.email'), id: 'input_email', class: 'form-control' %>
                </div>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:email].any? %>
            <span class='error'>
              <%= t('form.field.email') %>
              <%= resource.errors.messages[:email][0] %>
            </span>
          <% end %>
          <div class='form-group'>
            <%= f.label t('form.field.password'), :input_html => { :class => 'form-label' } %>
            <div class='input-group flex' id='password'>
              <div class='validate password-box'>
                <%= f.password_field :password, autocomplete: 'current-password', placeholder: t('form.field.password'), id: 'input_password', class: 'form-control form-control--password' %>
              </div>
              <div class="input-group-append">
                <span toggle=".form-control--password" class="input-group-text toggle-password fa fa-fw fa-eye-slash field-icon pe-4 cursor-pointer" style='border-left: none; line-height: 1.5rem'>
                </span>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:password].any? %>
            <span class='error'>
              <%= t('form.field.password') %>
              <%= resource.errors.messages[:password][0] %>
            </span>
          <% end %>
          <div class='loggedin-forgot-password-link-wrapper'>
            <div class='custom-control custom-checkbox custom-control-inline'>
              <input class='custom-control-input' id='customCheckInline1' name='rememberMe' type='checkbox'/>
              <label class='custom-control-label' for='customCheckInline1'> <%= t('app.keep_me_logged_in')%> </label>
            </div>
            <%= link_to t('form.button.forgot_password'), new_password_path(resource_name) %>
          </div>
          <div class='actions'></div>
          <%= f.submit t('form.button.sign_in'), id: 'loginBtn', class: 'btn btn-md w-100 mb-3 mt-auto btn-primary' %>
        <% end %>
        <div class='bottom-links'>
          <ul class='support-links'>
            <li>
              <a href='https://www.kylas.io/contact' target='_blank'>  <%= t('app.contact_us')%> </a>
            </li>
            <li class='separator'></li>
            <li>
              <a href='https://www.kylas.io/privacy' target='_blank'><%= t('app.privacy_policy')%></a>
            </li>
            <li class='separator'></li>
            <li>
              <a href='https://support.kylas.io' target='_blank'><%= t('app.support')%></a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  $(document).ready(function(){
    $('#signInForm').validate({
      rules: {
        'user[email]': 'required',
        'user[password]': 'required'
      },

      messages: {
        'user[email]': 'Please enter valid email',
        'user[password]': 'Please enter password'
      }
    });

    $(".toggle-password").click(function() {
      $(this).toggleClass("fa-eye-slash fa-eye");
      var input = $($(this).attr("toggle"));
      if (input.attr("type") == "password") {
        input.attr("type", "text");
      } else {
        input.attr("type", "password");
      }
    });
  })
</script>
