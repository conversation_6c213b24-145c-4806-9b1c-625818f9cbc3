<% flash.each do |name, msg| %>
  <% case name.to_s %>
    <% when 'success' %>
      <div class="alert alert-success alert-dismissible fade show flash">
        <i class="fas fa-check-circle" style="color:green;font-size:50px;margin-right:30px" ></i>
        <div>
          <h4 style="color:green" >Success!</h4>
          <p><%== msg %></p>
        </div>
        <span class="btn-close" data-bs-dismiss="alert" aria-label="Close"></span>
      </div>
    <% when 'danger' %>
      <div class="alert alert-success alert-dismissible fade show flash">
        <i class="fas fa-ban" style="color:#EE0000;font-size:50px;margin-right:30px" ></i>
        <div>
          <h4 style="color:#EE0000" >Error</h4>
          <p><%== msg %></p>
        </div>
        <span class="btn-close" data-bs-dismiss="alert" aria-label="Close"></span>
      </div>
    <% when 'alert' %>
      <div class="alert alert-success alert-dismissible fade show flash">
        <i class="fa-solid fa-circle-exclamation" style="color:#FFA200;font-size:50px;margin-right:30px" ></i>
        <div>
          <h4 style="color:#FFA200" >Warning</h4>
          <p><%== msg %></p>
        </div>
        <span class="btn-close" data-bs-dismiss="alert" aria-label="Close"></span>
      </div>
    <% when 'notice' %>
      <div class="alert alert-success alert-dismissible fade show flash">
        <div>
          <h4 style="color:#006DEE" >Information</h4>
          <p><%== msg %></p>
        </div>
        <span class="btn-close" data-bs-dismiss="alert" aria-label="Close"></span>
      </div>
  <% end %>
<% end %>
<% flash.clear %>

<script>
  $(document).ready(function(){
    setTimeout(function(){
      $('.flash').fadeOut();
    }, 3000);
  })
</script>