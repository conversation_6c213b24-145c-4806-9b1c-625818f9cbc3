<div class='authentication-layout'>
  <div class='kylas-background'></div>
  <div class='content-wrapper'>
    <div class='main-content'>
      <div class='authentication-form-wrapper'>
        <%= link_to t('form.button.back_to_login'), new_session_path(resource_name), class: 'link-primary' %>
        <h1 class='mt-3'> <%= t('password.forgot_your_password') %> </h1>
        <p class='mb-4'>
          <%= t('password.reset_link_with_instructions') %>          
        </p>
        <%= render 'users/shared/flash' %>
        <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f| %>
          <div class='form-group'>
            <%= f.label t('form.field.email'), :input_html => { :class => 'form-label' } %>
            <div class='text-field col-undefined'>
              <div class='form-group' id='email'>
                <div class='validate'>
                  <%= f.email_field :email, autocomplete: 'off', placeholder: t('form.field.email'), id: 'input_email', class: 'form-control' %>
                </div>
              </div>
            </div>
          </div>
          <% if resource.errors.any? && resource.errors.messages[:email].any? %>
            <span class='error'>
              <%= t('form.field.email') %>
              <%= resource.errors.messages[:email][0] %>
            </span>
          <% end %>
          <div class='actions'></div>
          <%= f.submit t('form.button.request_reset_link'), class: 'btn btn-md w-100 mb-3 mt-auto btn-primary' %>
        <% end %>
      </div>
    </div>
  </div>
</div>
