<% content_for :title, "Create New Report" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Create New Report</h1>
        <%= link_to "Back to Reports", reports_path, class: "btn btn-outline-secondary" %>
      </div>

      <%= form_with model: @report, local: true, class: "needs-validation", novalidate: true do |f| %>
        <% if @report.errors.any? %>
          <div class="alert alert-danger">
            <h6>Please fix the following errors:</h6>
            <ul class="mb-0">
              <% @report.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <div class="row">
          <!-- Basic Information -->
          <div class="col-lg-8">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">Basic Information</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <%= f.label :name, class: "form-label" %>
                  <%= f.text_field :name, class: "form-control", placeholder: "Enter report name", required: true %>
                  <div class="invalid-feedback">Please provide a report name.</div>
                </div>

                <div class="mb-3">
                  <%= f.label :description, class: "form-label" %>
                  <%= f.text_area :description, class: "form-control", rows: 3, placeholder: "Describe what this report analyzes..." %>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <%= f.label :report_type, "Report Type", class: "form-label" %>
                    <%= f.select :report_type, options_for_select([
                      ['Onboarding Analysis', 'onboarding'],
                      ['Usage Analysis', 'usage'],
                      ['Comparison Report', 'comparison'],
                      ['Custom Report', 'custom']
                    ]), { prompt: 'Select report type' }, { class: "form-select", required: true } %>
                    <div class="invalid-feedback">Please select a report type.</div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <%= f.label :data_source, "Data Source", class: "form-label" %>
                    <%= f.select :data_source, options_for_select([
                      ['Onboarding Data', 'onboarding'],
                      ['Usage Data', 'usage'],
                      ['Both Sources', 'both']
                    ]), { prompt: 'Select data source' }, { class: "form-select", required: true, id: "data_source_select" } %>
                    <div class="invalid-feedback">Please select a data source.</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Metrics Selection -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">Metrics to Include</h5>
              </div>
              <div class="card-body">
                <div id="metrics-container">
                  <p class="text-muted">Select a data source to see available metrics.</p>
                </div>
              </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Filters</h5>
                <button type="button" class="btn btn-sm btn-outline-primary" id="add-filter-btn">
                  <i class="fas fa-plus"></i> Add Filter
                </button>
              </div>
              <div class="card-body">
                <div id="filters-container">
                  <p class="text-muted">No filters added yet. Click "Add Filter" to create filters for your report.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Settings Sidebar -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">Report Settings</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <div class="form-check">
                    <%= f.check_box :is_public, class: "form-check-input" %>
                    <%= f.label :is_public, "Make this report public", class: "form-check-label" %>
                    <small class="form-text text-muted">Public reports can be viewed by all users.</small>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <%= f.check_box :is_scheduled, class: "form-check-input", id: "schedule_checkbox" %>
                    <%= f.label :is_scheduled, "Schedule automatic generation", class: "form-check-label" %>
                  </div>
                </div>

                <div class="mb-3" id="schedule_frequency_container" style="display: none;">
                  <%= f.label :schedule_frequency, "Frequency", class: "form-label" %>
                  <%= f.select :schedule_frequency, options_for_select([
                    ['Daily', 'daily'],
                    ['Weekly', 'weekly'],
                    ['Monthly', 'monthly'],
                    ['Quarterly', 'quarterly'],
                    ['Yearly', 'yearly']
                  ]), { prompt: 'Select frequency' }, { class: "form-select" } %>
                </div>
              </div>
            </div>

            <!-- Chart Configuration -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">Chart Configuration</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Default Chart Type</label>
                  <select name="report[chart_config][default_type]" class="form-select">
                    <option value="bar">Bar Chart</option>
                    <option value="line">Line Chart</option>
                    <option value="pie">Pie Chart</option>
                    <option value="table">Table</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
              <%= f.submit "Create Report", class: "btn btn-primary" %>
              <%= link_to "Cancel", reports_path, class: "btn btn-outline-secondary" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Hidden template for filter rows -->
<template id="filter-template">
  <div class="filter-row border rounded p-3 mb-3">
    <div class="row">
      <div class="col-md-3 mb-2">
        <label class="form-label">Filter Type</label>
        <select name="filters[][filter_type]" class="form-select filter-type-select">
          <option value="">Select type</option>
          <option value="date_range">Date Range</option>
          <option value="tenant_comparison">Tenant Comparison</option>
          <option value="metric_filter">Metric Filter</option>
          <option value="time_period_comparison">Time Period Comparison</option>
        </select>
      </div>
      <div class="col-md-3 mb-2">
        <label class="form-label">Field</label>
        <select name="filters[][field_name]" class="form-select field-name-select">
          <option value="">Select field</option>
        </select>
      </div>
      <div class="col-md-3 mb-2">
        <label class="form-label">Operator</label>
        <select name="filters[][operator]" class="form-select operator-select">
          <option value="">Select operator</option>
          <option value="equals">Equals</option>
          <option value="greater_than">Greater Than</option>
          <option value="less_than">Less Than</option>
          <option value="between">Between</option>
          <option value="in">In</option>
          <option value="not_in">Not In</option>
        </select>
      </div>
      <div class="col-md-2 mb-2">
        <label class="form-label">&nbsp;</label>
        <button type="button" class="btn btn-outline-danger w-100 remove-filter-btn">
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <label class="form-label">Value</label>
        <input type="text" name="filters[][filter_value]" class="form-control filter-value-input" placeholder="Enter filter value">
      </div>
    </div>
  </div>
</template>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle data source change
  const dataSourceSelect = document.getElementById('data_source_select');
  const metricsContainer = document.getElementById('metrics-container');
  
  dataSourceSelect.addEventListener('change', function() {
    updateMetricsOptions(this.value);
  });
  
  // Handle schedule checkbox
  const scheduleCheckbox = document.getElementById('schedule_checkbox');
  const scheduleContainer = document.getElementById('schedule_frequency_container');
  
  scheduleCheckbox.addEventListener('change', function() {
    scheduleContainer.style.display = this.checked ? 'block' : 'none';
  });
  
  // Handle add filter button
  const addFilterBtn = document.getElementById('add-filter-btn');
  const filtersContainer = document.getElementById('filters-container');
  
  addFilterBtn.addEventListener('click', function() {
    addFilterRow();
  });
  
  function updateMetricsOptions(dataSource) {
    // This would be populated via AJAX or predefined data
    // For now, showing placeholder
    if (dataSource) {
      metricsContainer.innerHTML = '<p class="text-muted">Metrics selection will be implemented based on data source.</p>';
    } else {
      metricsContainer.innerHTML = '<p class="text-muted">Select a data source to see available metrics.</p>';
    }
  }
  
  function addFilterRow() {
    const template = document.getElementById('filter-template');
    const clone = template.content.cloneNode(true);
    
    // Add event listener for remove button
    clone.querySelector('.remove-filter-btn').addEventListener('click', function() {
      this.closest('.filter-row').remove();
      updateFiltersPlaceholder();
    });
    
    filtersContainer.appendChild(clone);
    updateFiltersPlaceholder();
  }
  
  function updateFiltersPlaceholder() {
    const filterRows = filtersContainer.querySelectorAll('.filter-row');
    if (filterRows.length === 0) {
      filtersContainer.innerHTML = '<p class="text-muted">No filters added yet. Click "Add Filter" to create filters for your report.</p>';
    }
  }
});
</script>
