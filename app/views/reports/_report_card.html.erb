<div class="card h-100">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h6 class="card-title mb-0">
      <%= link_to report.name, report_path(report), class: "text-decoration-none" %>
    </h6>
    <div class="dropdown">
      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
        <i class="fas fa-ellipsis-v"></i>
      </button>
      <ul class="dropdown-menu">
        <li><%= link_to "View", report_path(report), class: "dropdown-item" %></li>
        <% if defined?(shared) && shared %>
          <li><span class="dropdown-item-text text-muted">Shared by <%= report.created_by.name %></span></li>
        <% elsif defined?(public) && public %>
          <li><span class="dropdown-item-text text-muted">Created by <%= report.created_by.name %></span></li>
        <% else %>
          <li><%= link_to "Edit", edit_report_path(report), class: "dropdown-item" %></li>
          <li><hr class="dropdown-divider"></li>
          <li>
            <%= link_to "Delete", report_path(report), method: :delete, 
                        class: "dropdown-item text-danger",
                        confirm: "Are you sure you want to delete this report?" %>
          </li>
        <% end %>
      </ul>
    </div>
  </div>
  
  <div class="card-body">
    <% if report.description.present? %>
      <p class="card-text text-muted small mb-2"><%= truncate(report.description, length: 100) %></p>
    <% end %>
    
    <div class="row mb-2">
      <div class="col-6">
        <small class="text-muted">Type:</small><br>
        <span class="badge bg-primary"><%= report.report_type.humanize %></span>
      </div>
      <div class="col-6">
        <small class="text-muted">Data Source:</small><br>
        <span class="badge bg-info"><%= report.data_source.humanize %></span>
      </div>
    </div>
    
    <div class="row mb-2">
      <div class="col-6">
        <small class="text-muted">Created:</small><br>
        <small><%= report.created_at.strftime("%b %d, %Y") %></small>
      </div>
      <div class="col-6">
        <small class="text-muted">Last Run:</small><br>
        <% if report.last_generated_at %>
          <small><%= time_ago_in_words(report.last_generated_at) %> ago</small>
        <% else %>
          <small class="text-muted">Never</small>
        <% end %>
      </div>
    </div>
    
    <% if report.is_scheduled? %>
      <div class="mb-2">
        <small class="text-muted">Schedule:</small><br>
        <span class="badge bg-warning text-dark">
          <i class="fas fa-clock"></i> <%= report.schedule_frequency.humanize %>
        </span>
      </div>
    <% end %>
    
    <% if report.is_public? %>
      <div class="mb-2">
        <span class="badge bg-success">
          <i class="fas fa-globe"></i> Public
        </span>
      </div>
    <% end %>
  </div>
  
  <div class="card-footer bg-transparent">
    <div class="d-flex justify-content-between align-items-center">
      <% latest_result = report.latest_result %>
      <% if latest_result %>
        <% if latest_result.completed? %>
          <span class="badge bg-success">
            <i class="fas fa-check"></i> Completed
          </span>
        <% elsif latest_result.failed? %>
          <span class="badge bg-danger">
            <i class="fas fa-times"></i> Failed
          </span>
        <% elsif latest_result.processing? %>
          <span class="badge bg-warning text-dark">
            <i class="fas fa-spinner fa-spin"></i> Processing
          </span>
        <% else %>
          <span class="badge bg-secondary">
            <i class="fas fa-clock"></i> Pending
          </span>
        <% end %>
      <% else %>
        <span class="badge bg-light text-dark">Not Generated</span>
      <% end %>
      
      <div class="btn-group btn-group-sm">
        <%= link_to report_path(report), class: "btn btn-outline-primary btn-sm" do %>
          <i class="fas fa-eye"></i>
        <% end %>
        <% unless defined?(shared) && shared || defined?(public) && public %>
          <%= link_to generate_report_path(report), method: :post, class: "btn btn-outline-success btn-sm" do %>
            <i class="fas fa-play"></i>
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
</div>
