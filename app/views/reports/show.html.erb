<% content_for :title, @report.name %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <!-- Header -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 class="h3 mb-1"><%= @report.name %></h1>
          <% if @report.description.present? %>
            <p class="text-muted mb-0"><%= @report.description %></p>
          <% end %>
        </div>
        <div class="btn-group">
          <%= link_to "Back to Reports", reports_path, class: "btn btn-outline-secondary" %>
          <% if @can_edit %>
            <%= link_to "Edit", edit_report_path(@report), class: "btn btn-outline-primary" %>
          <% end %>
          <%= link_to "Generate", generate_report_path(@report), method: :post, class: "btn btn-success" %>
          <% if @can_export && @latest_result&.completed? %>
            <div class="btn-group">
              <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                Export
              </button>
              <ul class="dropdown-menu">
                <li><%= link_to "Export as CSV", export_report_path(@report, format: 'csv'), class: "dropdown-item" %></li>
                <li><%= link_to "Export as JSON", export_report_path(@report, format: 'json'), class: "dropdown-item" %></li>
              </ul>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Report Info Cards -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <h6 class="card-title">Report Type</h6>
              <span class="badge bg-primary fs-6"><%= @report.report_type.humanize %></span>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <h6 class="card-title">Data Source</h6>
              <span class="badge bg-info fs-6"><%= @report.data_source.humanize %></span>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <h6 class="card-title">Last Generated</h6>
              <% if @report.last_generated_at %>
                <small><%= time_ago_in_words(@report.last_generated_at) %> ago</small>
              <% else %>
                <small class="text-muted">Never</small>
              <% end %>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card">
            <div class="card-body text-center">
              <h6 class="card-title">Status</h6>
              <% if @latest_result %>
                <% if @latest_result.completed? %>
                  <span class="badge bg-success">Completed</span>
                <% elsif @latest_result.failed? %>
                  <span class="badge bg-danger">Failed</span>
                <% elsif @latest_result.processing? %>
                  <span class="badge bg-warning text-dark">Processing</span>
                <% else %>
                  <span class="badge bg-secondary">Pending</span>
                <% end %>
              <% else %>
                <span class="badge bg-light text-dark">Not Generated</span>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Report Results -->
      <% if @latest_result %>
        <% if @latest_result.completed? %>
          <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">Report Results</h5>
              <small class="text-muted">
                Generated on <%= @latest_result.generated_at.strftime("%B %d, %Y at %I:%M %p") %>
                (<%= @latest_result.record_count %> records)
              </small>
            </div>
            <div class="card-body">
              <!-- Summary Stats -->
              <% if @latest_result.summary_stats.present? %>
                <div class="row mb-4">
                  <% @latest_result.summary_stats.each do |key, value| %>
                    <div class="col-md-3 mb-2">
                      <div class="border rounded p-3 text-center">
                        <h4 class="mb-1"><%= value %></h4>
                        <small class="text-muted"><%= key.humanize %></small>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% end %>

              <!-- Chart Visualization -->
              <% if @latest_result.chart_data.present? %>
                <div class="mb-4">
                  <canvas id="reportChart" width="400" height="200"></canvas>
                </div>
              <% end %>

              <!-- Data Table -->
              <% if @latest_result.result_data.present? && @latest_result.result_data.is_a?(Array) %>
                <div class="table-responsive">
                  <table class="table table-striped table-hover">
                    <thead class="table-dark">
                      <tr>
                        <% if @latest_result.result_data.first.is_a?(Hash) %>
                          <% @latest_result.result_data.first.keys.each do |key| %>
                            <th><%= key.humanize %></th>
                          <% end %>
                        <% end %>
                      </tr>
                    </thead>
                    <tbody>
                      <% @latest_result.result_data.first(50).each do |row| %>
                        <tr>
                          <% if row.is_a?(Hash) %>
                            <% row.values.each do |value| %>
                              <td><%= value %></td>
                            <% end %>
                          <% end %>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                  <% if @latest_result.result_data.size > 50 %>
                    <p class="text-muted text-center">
                      Showing first 50 of <%= @latest_result.record_count %> records. 
                      <%= link_to "Export full data", export_report_path(@report, format: 'csv'), class: "text-decoration-none" %> to see all results.
                    </p>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        <% elsif @latest_result.failed? %>
          <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> Report Generation Failed</h6>
            <p class="mb-0"><%= @latest_result.error_message %></p>
          </div>
        <% elsif @latest_result.processing? %>
          <div class="alert alert-info">
            <h6><i class="fas fa-spinner fa-spin"></i> Report is Processing</h6>
            <p class="mb-0">Your report is currently being generated. Please refresh the page in a few moments.</p>
          </div>
        <% else %>
          <div class="alert alert-secondary">
            <h6><i class="fas fa-clock"></i> Report Generation Pending</h6>
            <p class="mb-0">Report generation is queued. It will start processing shortly.</p>
          </div>
        <% end %>
      <% else %>
        <div class="card">
          <div class="card-body text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Data Generated Yet</h4>
            <p class="text-muted">Click "Generate" to run this report and see the results.</p>
            <%= link_to "Generate Report", generate_report_path(@report), method: :post, class: "btn btn-primary" %>
          </div>
        </div>
      <% end %>

      <!-- Report Configuration -->
      <div class="row mt-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">Report Configuration</h6>
            </div>
            <div class="card-body">
              <dl class="row">
                <dt class="col-sm-4">Created by:</dt>
                <dd class="col-sm-8"><%= @report.created_by.name %></dd>
                
                <dt class="col-sm-4">Created on:</dt>
                <dd class="col-sm-8"><%= @report.created_at.strftime("%B %d, %Y") %></dd>
                
                <dt class="col-sm-4">Visibility:</dt>
                <dd class="col-sm-8">
                  <% if @report.is_public? %>
                    <span class="badge bg-success">Public</span>
                  <% else %>
                    <span class="badge bg-secondary">Private</span>
                  <% end %>
                </dd>
                
                <% if @report.is_scheduled? %>
                  <dt class="col-sm-4">Schedule:</dt>
                  <dd class="col-sm-8">
                    <span class="badge bg-warning text-dark">
                      <i class="fas fa-clock"></i> <%= @report.schedule_frequency.humanize %>
                    </span>
                  </dd>
                <% end %>
              </dl>
            </div>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6 class="card-title mb-0">Active Filters</h6>
            </div>
            <div class="card-body">
              <% if @report_filters.any? %>
                <% @report_filters.each do |filter| %>
                  <div class="mb-2">
                    <span class="badge bg-light text-dark">
                      <%= filter.filter_type.humanize %>: 
                      <%= filter.field_name %> 
                      <%= filter.operator %>
                      <%= filter.filter_value %>
                    </span>
                  </div>
                <% end %>
              <% else %>
                <p class="text-muted mb-0">No filters applied</p>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<% if @latest_result&.chart_data.present? %>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const ctx = document.getElementById('reportChart').getContext('2d');
  const chartData = <%= @latest_result.chart_data.to_json.html_safe %>;
  
  new Chart(ctx, {
    type: chartData.type || 'bar',
    data: chartData.data,
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: '<%= @report.name %>'
        }
      }
    }
  });
});
</script>
<% end %>
