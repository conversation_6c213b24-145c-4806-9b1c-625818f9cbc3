<% content_for :title, "Reports" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Reports</h1>
        <%= link_to "Create New Report", new_report_path, class: "btn btn-primary" %>
      </div>

      <!-- Filter Tabs -->
      <ul class="nav nav-tabs mb-4" id="reportTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="my-reports-tab" data-bs-toggle="tab" data-bs-target="#my-reports" type="button" role="tab">
            My Reports (<%= @reports.count %>)
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="shared-reports-tab" data-bs-toggle="tab" data-bs-target="#shared-reports" type="button" role="tab">
            Shared with Me (<%= @shared_reports.count %>)
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="public-reports-tab" data-bs-toggle="tab" data-bs-target="#public-reports" type="button" role="tab">
            Public Reports (<%= @public_reports.count %>)
          </button>
        </li>
      </ul>

      <!-- Filter Controls -->
      <div class="row mb-3">
        <div class="col-md-4">
          <%= form_with url: reports_path, method: :get, local: true, class: "d-flex" do |f| %>
            <%= f.select :type, options_for_select([
              ['All Types', ''],
              ['Onboarding', 'onboarding'],
              ['Usage', 'usage'],
              ['Comparison', 'comparison'],
              ['Custom', 'custom']
            ], params[:type]), {}, { class: "form-select me-2" } %>
            <%= f.select :data_source, options_for_select([
              ['All Sources', ''],
              ['Onboarding Data', 'onboarding'],
              ['Usage Data', 'usage'],
              ['Both Sources', 'both']
            ], params[:data_source]), {}, { class: "form-select me-2" } %>
            <%= f.submit "Filter", class: "btn btn-outline-secondary" %>
          <% end %>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="tab-content" id="reportTabsContent">
        <!-- My Reports Tab -->
        <div class="tab-pane fade show active" id="my-reports" role="tabpanel">
          <% if @reports.any? %>
            <div class="row">
              <% @reports.each do |report| %>
                <div class="col-md-6 col-lg-4 mb-4">
                  <%= render 'report_card', report: report %>
                </div>
              <% end %>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
              <%== pagy_bootstrap_nav(@pagy_reports) if @pagy_reports.pages > 1 %>
            </div>
          <% else %>
            <div class="text-center py-5">
              <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
              <h4 class="text-muted">No reports found</h4>
              <p class="text-muted">Create your first report to get started with data analysis.</p>
              <%= link_to "Create Report", new_report_path, class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>

        <!-- Shared Reports Tab -->
        <div class="tab-pane fade" id="shared-reports" role="tabpanel">
          <% if @shared_reports.any? %>
            <div class="row">
              <% @shared_reports.each do |report| %>
                <div class="col-md-6 col-lg-4 mb-4">
                  <%= render 'report_card', report: report, shared: true %>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-5">
              <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
              <h4 class="text-muted">No shared reports</h4>
              <p class="text-muted">Reports shared with you will appear here.</p>
            </div>
          <% end %>
        </div>

        <!-- Public Reports Tab -->
        <div class="tab-pane fade" id="public-reports" role="tabpanel">
          <% if @public_reports.any? %>
            <div class="row">
              <% @public_reports.each do |report| %>
                <div class="col-md-6 col-lg-4 mb-4">
                  <%= render 'report_card', report: report, public: true %>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-5">
              <i class="fas fa-globe fa-3x text-muted mb-3"></i>
              <h4 class="text-muted">No public reports</h4>
              <p class="text-muted">Public reports will appear here when available.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
