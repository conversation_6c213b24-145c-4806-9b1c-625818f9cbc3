<%= render layout:'account_details/shared/tabs_layout', locals: { account: @account_detail, tenant: @account_detail.tenant } do %>
  <div class="plan-detail-container">
    <div class="plan-detail">
      <div class="horizontal-container">
        <div class="first-element">
          <%= image_tag("plan_logo.png") %>
            <div class="text-container">
              <h3 class="plan-name"> <%= @plan_name.titleize%> </h3>
              <span> <%= @currency_code %> <h5 class="price"><%= (@plan_price/100).to_fs(:delimited) %></span>
                <span class="month"> / <%= @billing_period_unit %>
                </span> <span class="tax">(<%= t('plan_details.excluding_tax') %>)</span>
              </h5>
            </div>
        </div>

        <div class="other-elements">
          <div class="vr"></div>
          <div>
            <h3 class="heading-text"> <%= t('plan_details.status')%> </h3>
            <% if @plan_status.capitalize == "Active" %>
              <h4 class="active"> <%= @plan_status.capitalize%> </h4>
            <% else %>
              <h4 class="inactive"> <%= @plan_status.capitalize%> </h4>
            <% end %>
          </div>
          <div class="vr"></div>
            <div>
              <h3 class="heading-text">MRR</h3>
              <h5 class="sub-text"> <%= @mrr %> </h5>
            </div>
          <div class="vr"></div>
          <div>
            <h3 class="heading-text"><%= t('plan_details.next_renewal')%></h3>
            <h5 class="sub-text next-renewal"> <%= @next_renewal %> </h5>
          </div>
        </div>
      </div>
    </div>
    <hr>

    <div class="section add-on">
      <div class="add-on-text"><%= t('plan_details.add_ons')%></div>
      <table class="table table-bordered entity-table">
        <thead class="table-head">
          <tr>
            <th style="width:25%;">Add On</th>
            <th style="width:15%;">Quantity</th>
            <th style="width:15%;">Unit Price</th>
            <th style="width:15%;">Amount</th>
          </tr>
        </thead>
        <tbody>
          <% @add_on.each do |add_on| %>
            <tr>
              <td><%= add_on[:name].titleize %></td>
              <td><%= add_on[:quantity] %></td>
              <td><%= add_on[:unit_price] / 100 %></td>
              <td><%= add_on[:amount] / 100 %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
<% end %>

<script>
  $('.updated-at').each(function() {
    $(this).text(`${moment($(this).text()).local().format('lll')}`)
  });

  $('.next-renewal').each(function() {
    $(this).text(`${moment($(this).text()).local().format('ll')}`)
  });
</script>
