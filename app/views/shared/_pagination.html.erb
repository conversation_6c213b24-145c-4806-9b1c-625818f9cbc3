<% link = pagy_link_proc(pagy, link_extra: 'class="page-link"') %>
<div class="results-text" >
  <%= t('pagination.showing_items', from: pagy.from, to: pagy.to, count: pagy.count) %>
</div>
<div class="pgn-grp" >
  <div class="results-input-container" >
   <%= t('pagination.results_per_page') %>&nbsp &nbsp 
    <div class="btn-group dropup border">
      <button type="button" class="btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <%= pagy.items %>
      </button>
      <div class="dropdown-menu">
        <div class="dropdown-menu-container" >
          <%= link_to "10", path.call(items: 10, page: 1) %>
          <%= link_to "15", path.call(items: 15, page: 1) %>
          <%= link_to "20", path.call(items: 20, page: 1) %>
        </div>
      </div>
    </div>
  </div>
  <div class="pgn-container"> 
    <nav class="pagy-bootstrap-nav" role="navigation">
    <ul class="pagination">
      <% if pagy.page > 1 -%>    
        <li class="page-item prev">
          <%== link.call(1, t('pagination.first'), 'aria-label="first"') %>
        </li>
      <% else -%>    
        <li class="page-item prev disabled">
          <a href="#" class="page-link">
            <%= t('pagination.first') %>
          </a>
        </li>
      <% end -%>
      <% pagy.series.each do |item| -%>
        <% if item.is_a?(Integer) -%>    
          <li class="page-item">
            <%== link.call(item) %> 
          </li>
        <% elsif item.is_a?(String) -%>
          <li class="page-item active">
            <%== link.call(item) %>
          </li>
        <% elsif item == :gap -%>    
          <li class="page-item disabled gap"><a href="#" class="page-link"><%== pagy_t('pagy.nav.gap') %></a></li>
        <% end -%>
      <% end -%>
      <% if pagy.page < pagy.last -%>    
        <li class="page-item next">
          <%== link.call(pagy.last, t('pagination.last'), 'aria-label="last"') %>
        </li>
      <% else -%>    
        <li class="page-item next disabled">
          <a href="#" class="page-link">
            <%= t('pagination.last') %>
          </a>
        </li>
      <% end -%>
    </ul>
    </nav>
  </div>
</div>
