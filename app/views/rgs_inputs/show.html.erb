<%= render layout:'account_details/shared/tabs_layout', locals: { account: @account_detail, tenant: @rgs_input.tenant } do %>

  <div class="rgs-input-container">
    <%= form_for @rgs_input, url: rgs_inputs_account_detail_url , method: :post do |f| %>
      <div class="section text-area">
        <%= t('rgs_inputs.header_text')%>
      </div>
    <hr>

    <div class="section for-input-fields">
      <div class="onboarding-text mb-4"> <%= t('rgs_inputs.user_onboarded_text')%> </div>
        <div class="row">
          <div class="col-sm-6">
            <div class="form-group d-flex align-items-center">
              <div class="flex-grow-1">
                <%= f.label :total_users, t('rgs_inputs.no_of_users_field'), class:"onboarding-sub-text" %>
              </div>
              <div class="flex-grow-1">
                <%= f.number_field :total_users, class: "form-control", placeholder: t('rgs_inputs.user_placeholder'), min: 0 %>
              </div>
            </div>
          </div>
          <div class="col-sm-6">
            <div class="form-group d-flex align-items-center">
            <div class="v-line"></div>
              <div class="flex-grow-1">
                <%= f.label :total_managers, t('rgs_inputs.no_of_manager_field'), class:"onboarding-sub-text" %>
              </div>
              <div class="flex-grow-1">
                <%= f.number_field :total_managers, class: "form-control", placeholder: t('rgs_inputs.manager_placeholder'), min: 0 %>
              </div>
            </div>
          </div>
        </div>
    </div>
    <hr>

    <div class="section for-entity">
      <div class="onboarding-text"> <%= t('rgs_inputs.sales_rgs_input')%> </div>
        <div class="entity-table-wrapper">
          <table class="table table-bordered entity-table">
            <thead class="table-head">
              <tr>
                <th style="width:25%;"> <%= t('rgs_inputs.table.entity')%> </th>
                <th style="width:15%;"><%= t('rgs_inputs.table.critical')%></th>
                <th><%= t('rgs_inputs.table.volume')%></th>
              </tr>
            </thead>
            <tbody class="entity_fields">
              <%= f.fields_for :entities do |entity_form| %>
                <%= render 'entity_fields', f: entity_form %>
              <% end %>
            </tbody>
        </table>
        </div>
      <div class="mt-4">
        <%= link_to_add_association f, :entities, class: 'save-btn-rgs add-entity-row',
        data: { association_insertion_node: '.entity_fields', association_insertion_method: :append } do %>
          <i class="fa-solid fa-plus"></i> <%= t('rgs_inputs.table.add')%>
        <% end %>
      </div>
    </div>
    <hr>

    <div class="section for-marketplace">
      <div class="onboarding-text"><%= t('rgs_inputs.marketplace_rgs_input')%> </div>
        <div class="Kylas Marketplace RGS input"></div>
          <div class="marketplace-app-table-wrapper">
            <table class="table table-bordered marketplace_app-table">
              <thead class="table-head">
                <tr>
                  <th style="width: 40%;"><%= t('rgs_inputs.table.marketplace_apps')%></th>
                  <th><%= t('rgs_inputs.table.integrated')%></th>
                </tr>
              </thead>
              <tbody class="marketplace-app-fields">
                <%= f.fields_for :marketplace_apps do |marketplace_app_form| %>
                  <%= render 'marketplace_app_fields', f: marketplace_app_form %>
                <% end %>
              </tbody>
            </table>
          </div>

      <div class="links mt-3">
        <%= link_to_add_association f, :marketplace_apps, class: 'save-btn-rgs',
        data: { association_insertion_node: '.marketplace-app-fields', association_insertion_method: :append } do %>
          <i class="fa-solid fa-plus"></i> <%= t('rgs_inputs.table.add')%>
        <% end %>
      </div>
    </div>
    <hr>

    <div class="section for-unverified-marketplace-apps">
      <div class="onboarding-text"> <%= t('rgs_inputs.unverified_marketplace_apps')%> </div>
        <div class="unverified-marketplace-table-wrapper">
          <% if @unverfied_apps.present? %>
            <table class="table table-bordered marketplace_app-table">
              <thead class="table-head">
                <tr>
                  <th style="width: 40%;"><%= t('rgs_inputs.table.marketplace_apps')%></th>
                  <th><%= t('rgs_inputs.table.integrated')%></th>
                </tr>
              </thead>
              <% @unverfied_apps.each do |unverfied_app| %>
                <tbody class="unverified-marketplace-app-fields">
                  <td>
                    <div class="unverified-marketplace-app-name-field">
                      <%= text_field_tag :name, unverfied_app, class: "unverified-marketplace-app-text-field", disabled: true %>
                    </div>
                  </td>
                  <td>
                    <div class="integrated-button-group">
                      <div class="button-group">
                        <%= label_tag t('rgs_inputs.table.radio_yes') %>
                      </div>
                    </div>
                  </td>
                </tbody>
              <% end %>
            </table>
          <% else %>
            <h6 class="mt-3"><%= t('rgs_inputs.no_unverfied_app')%></h6>
          <% end %>
        </div>
    </div>

    <div class="button save-cancel-button">
      <%= link_to t('rgs_inputs.table.cancel'), rgs_inputs_account_detail_path, class: "btn btn-outline-primary cancel-btn" %>
      <%= f.submit t('rgs_inputs.table.save'), class: "btn save-btn", data: { disable_with: t('rgs_inputs.table.saving') } %>
    </div>
    <% end %>
  </div>
<% end %>

<script>
  var selectedValues = [];

  function updateDropdownOptions() {
    $('.entity-category-field').each(function() {
      var currentValue = this.value;
      $(this).find('option').each(function() {
        var optionValue = this.value;
        if (optionValue !== '' && selectedValues.includes(optionValue) && optionValue !== currentValue) {
          $(this).prop('disabled', true);
        } else {
          $(this).prop('disabled', false);
        }
      });
    });
  }

  $(document).on('click', '.add-entity-row', function() {
    var currentValue = $(this).closest('.nested-fields').find('.entity-category-field').val();
    if (currentValue !== '') {
      selectedValues.push(currentValue);
    }
    updateDropdownOptions();
  });

  $(document).on('click', '.remove-entity-row', function() {
    var removedValue = $(this).closest('.nested-fields').find('.entity-category-field').val();
    selectedValues = selectedValues.filter(function(value) {
      return value !== removedValue;
    });
    updateDropdownOptions();
  });

  $(document).on('change', '.entity-category-field', function() {
    var currentValue = $(this).val();
    var previousValue = $(this).data('previous-value');
    selectedValues = selectedValues.filter(function(value) {
      return value !== previousValue;
    });
    if (currentValue !== '') {
      selectedValues.push(currentValue);
    }
    $(this).data('previous-value', currentValue);
    updateDropdownOptions();
  });

  $(document).ready(function() {
    $('.entity-category-field').each(function() {
      var currentValue = this.value;
      if (currentValue !== '') {
        selectedValues.push(currentValue);
      }
    });
  });
</script>
