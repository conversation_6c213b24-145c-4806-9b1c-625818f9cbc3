<tr class="nested-fields marketplace-app-row">
  <td>
    <div class="marketplace-app-name-field">
      <%= f.select :name, options_for_select(@marketplace_apps, f.object.name), { selected: f.object.name },
        class: "marketplace-app-select form-control-text", required: true,
        disabled: @apps_to_disable.include?(f.object.name) %>
    </div>
  </td>

  <td> 
    <div class="integrated-button-group">
      <div class="button-group">
        <% if @apps_to_disable.include?(f.object.name) %>
          <%= f.hidden_field :integrated, value: true %>
          <%= label_tag t('rgs_inputs.table.radio_yes') %>
        <% else %>
          <%= f.hidden_field :integrated, value: false %>
          <%= label_tag t('rgs_inputs.table.radio_no') %> 
        <% end %>
      </div>
      <%= link_to_remove_association f, onclick: "removeRequired(event)", hidden: @apps_to_disable.include?(f.object.name) do %>
        <i class="fa-solid fa-trash"></i>
      <% end %>
    </div>
  </td>
</tr>

<script>
  $(document).ready(function() {
    $('.marketplace-app-select').select2({
      placeholder: 'Choose',
      allowClear: true
    });
  })

  function removeRequired(event) {
    const row = event.target.closest('.nested-fields');
    const inputs = row.querySelectorAll('.form-control-text');
    inputs.forEach(input => {
      input.required = false;
    });
  };
</script>
