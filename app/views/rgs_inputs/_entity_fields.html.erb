  <tr class="nested-fields entity-field-row">
    <td class="button-group">
      <%= f.select :category, options_for_select(ENTITY_CATEGORY.map { |option| [option.titleize, option] }, f.object.category), {},
      class: 'entity-category-field form-control-text', required: true %>
    </td>

    <td>
      <%= f.radio_button :critical, true, value: true, required: true, class:"mr-3" %> <%= label_tag t('rgs_inputs.table.radio_yes') %>
      <%= f.radio_button :critical, false, value: false %> <%= label_tag t('rgs_inputs.table.radio_no') %>
    </td>

    <td>
      <div class="expected-volume-container">
        <div class="button-group">
          <%= f.number_field :expected_volume, class: "form-control-text", placeholder: t('rgs_inputs.table.volume_placeholder'), required: true, min: 0 %>
          <%= f.text_field :frequency, value: 'DAILY', readonly: true %>
        </div>
        <%= link_to_remove_association f, onclick: "removeRequired(event)", class: "remove-entity-row" do %>
          <i class="fa-solid fa-trash"></i>
        <% end %>
      </div>
    </td>
  </tr>


<script>
  function removeRequired(event) {
    const row = event.target.closest('.nested-fields');
    const inputs = row.querySelectorAll('.form-control-text');
    inputs.forEach(input => {
      input.required = false;
    });
  };

  $(document).ready(function() {
    $('.entity-category-field').select2({
      placeholder: 'Choose',
      allowClear: true
    });
  });
</script>
