<div class="search-bar-container" >
  <div class="heading" >
    <%= t('account_list.all_kylas_accounts') %>
  </div>
  <div class="search-grp" >
    <%= link_to account_details_path(page: @pagy.page, items: @pagy.items, key: @search_key), class:"search-btn" do %>
      <i class="fa fa-refresh" ></i>
    <% end %>
    <%= form_with url: account_details_path, method: :get do |f| %>
      <div class="input-container">
        <%= f.text_field :key, class: "search-bar", placeholder: t('account_details.form.search') , value: @search_key %>
        <i class="fa fa-search"></i>
      </div>
    <% end %>
  </div>
</div>
<div class="list-status" >
    <%= t('account_list.latest_list', count: @accounts.count) %>
</div>
<div class="data-container" >
  <div class="data-list" >
    <div class="table-responsive table-container" >
      <table class="table">
        <tr >
          <th>
            <%= t('account_list.kylas_tenant_id') %>
            <i class="fa-solid fa-caret-down"></i>
          </th>
          <th>
            <%= t('account_list.name') %>
            <i class="fa-solid fa-caret-down"></i>
          </th>
          <th>
            <%= t('account_list.email') %>
            <i class="fa-solid fa-caret-down"></i>
          </th>
          <th>
            <%= t('account_list.industry') %>
            <i class="fa-solid fa-caret-down"></i>
          </th>
          <th>
            <%= t('account_list.current_plan') %>
            <i class="fa-solid fa-caret-down"></i>
          </th>
        </tr>
        <% @accounts.map do |account| %>
        <tr >
            <td><%= link_to_account_detail(account.kylas_tenant_id, account) %></td>
            <td><%= link_to_account_detail(account&.tenant&.name, account) %></td>
            <td><%= link_to_account_detail(account.email, account) %></td>
            <td><%= link_to_account_detail(account.industry&.titleize, account) %></td>
            <td><%= link_to_account_detail(account.plan_name&.titleize, account) %></td>
          </tr>
        <% end %>
      </table>
    </div>
    <div class="data-footer" >
      <%= render 'shared/pagination', pagy: @pagy, path: method(:account_details_path) %>
    </div>
  </div>
</div>

<script>
  $('.search-bar').keypress(function(e){
    if(e.which == 13){
      $(this).closest('form').submit();
    }
  });

  $('.date-time').each(function() {
    $(this).text( `${moment($(this).text()).local().format('MMM D, YYYY')}` != 'Invalid date' ? `${moment($(this).text()).local().format('MMM D, YYYY')}` : "-")
  });
</script>