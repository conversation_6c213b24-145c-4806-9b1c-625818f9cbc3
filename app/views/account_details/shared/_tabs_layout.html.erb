<div class="heading-container" >
  <div class="heading" >
    <div class="kylas-icon">
      K
    </div>
    <strong>
      <%= t('account_details.kylas_growth_engine')%>
    </strong>
  </div>
  <div class="tenant-id-box" >
    <strong> <%= t('account_details.kylas_tenant_id')%></strong><%= "#{account.kylas_tenant_id}" %>
  </div>
</div>
<div class="data-container mt-3" >
  <div class="tabs">
    <%= link_to t('account_details.account_details'), account_detail_path(tenant.account_detail), class: nav_link_class(default_class: "tab", active_class:"tab active", condition: current_page?(account_detail_path(tenant.account_detail))) if tenant&.account_detail %>
    <%= link_to t('account_details.plan_details'), plan_details_account_detail_path, class: nav_link_class(default_class: "tab", active_class:"tab active", condition: current_page?(plan_details_account_detail_path)) %>
    <%= link_to t('account_details.rgs_inputs'), rgs_inputs_account_detail_path, class: nav_link_class(default_class: "tab", active_class:"tab active", condition: current_page?(rgs_inputs_account_detail_path))  %>
    <%= link_to t('account_details.customer_ask'), customer_asks_account_detail_path, class: nav_link_class(default_class: "tab", active_class:"tab active", condition: current_page?(customer_asks_account_detail_path) ) %>
  </div>
    <div class="tab-panel">
      <%= yield %>
    </div>
</div>

<script>
  $(document).ready(() => {
    $(".tab").click((e) => {
      $(".tab-panel").detach();
      var loader = "<div class='loader'></div>"
      $(loader).appendTo(".data-container");
      $(".tab").attr('class', 'tab')
      $(e.currentTarget).attr('class','tab active')
    });
  });
</script>
