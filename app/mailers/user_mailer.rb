# frozen_string_literal: true

class UserMailer < ApplicationMailer
  def account_deactivated
    @email = params[:email]
    @admin_email = params[:admin_email]
    mail(to: @email, subject: I18n.t('mailer.user_deactivated.subject'))
  end

  def account_activated
    @email = params[:email]
    @admin_email = params[:admin_email]
    mail(to: @email, subject: I18n.t('mailer.user_activated.subject'))
  end
end
