# frozen_string_literal: true

class RgsInputsController < ApplicationController

  before_action :authenticate_user!, :authenticate_status
  before_action :set_tenant_and_rgs_input, only: %i[show update]

  def show
    @rgs_input.entities.build if @rgs_input.entities.empty?
    @rgs_input.marketplace_apps.build if @rgs_input.marketplace_apps.empty?
  end

  def update
    @rgs_input.tenant_id = @tenant_id
    if @rgs_input.update(rgs_input_params)
      flash[:success] = t('rgs_inputs.success')
    else
      flash[:danger] = t('rgs_inputs.error')
    end
    redirect_to rgs_inputs_account_detail_path
  end

  private

  def rgs_input_params
    params.require(:rgs_input).permit( :total_users, :total_managers, :tenant_id,
      entities_attributes: [ :id, :critical,  :expected_volume, :category, :frequency, :rgs_input_id, :_destroy ],
      marketplace_apps_attributes: [ :id, :name, :integrated, :rgs_input_id, :_destroy ])
  end

  def set_tenant_and_rgs_input
    @account_detail = AccountDetail.find_by(id: params[:id])
    unless @account_detail
      redirect_to account_details_path, notice: t('account_details.not_found')
      return
    end
    @tenant_id = @account_detail.tenant_id
    @rgs_input = RgsInput.find_or_initialize_by(tenant_id: @tenant_id)
    get_marketplace_apps_from_account(@account_detail)
  end

  def get_marketplace_apps_from_account(account_detail)
    @marketplace_apps = MarketplaceAppsData.call || []
    apps = account_detail.marketplace_apps_installed
    unavailable_apps = apps - @rgs_input.marketplace_apps.where(name: apps).pluck(:name)
    apps_to_build = @marketplace_apps & unavailable_apps
    @rgs_input.save unless @rgs_input.nil?

    unless apps_to_build.empty?
      data = apps_to_build.map { |app| { name: app, integrated: true } }
      @rgs_input.marketplace_apps.insert_all(data)
    end

    @apps_to_disable = @marketplace_apps & apps
    @unverfied_apps = apps - @marketplace_apps
  end

  def authenticate_status
    if current_user.deactivated
      flash[:danger] = I18n.t('devise.failure.no_permission')
      sign_out(current_user)
      redirect_to new_user_session_path
    end
  end
end
