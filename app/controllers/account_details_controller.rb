# frozen_string_literal: true

class AccountDetailsController < ApplicationController
  include Pagy::Backend
  before_action :authenticate_user!, :authenticate_status
  before_action :account_exists, only: %i[show update]

  def index
    @search_key = params[:key]
    if @search_key.blank?
      @pagy, @accounts = pagy(AccountDetail.includes(:tenant), items: params[:items])
    elsif @search_key.scan(/\D/).empty?
      @pagy, @accounts = pagy(AccountDetail.where('kylas_tenant_id::text LIKE ?', "%#{@search_key}%"), items: params[:items])
    else
      @pagy, @accounts = pagy(AccountDetail.includes(:tenant).joins(:tenant).where('LOWER(tenants.name) LIKE ? or LOWER(email) LIKE ?', "%#{@search_key.downcase}%", "%#{@search_key.downcase}%"), items: params[:items])
    end
  end

  def show; end

  def update
    @account.account_manager_id = account_details_params[:account_manager_id]
    @account.support_executive_id = account_details_params[:support_executive_id]
    @account.last_updated_by_id = current_user.id
    @account.last_updated_at = Time.now
    begin
      @account.save
      flash[:success] = I18n.t('success.account_details_updated')
    rescue ActiveRecord::InvalidForeignKey
      flash[:danger] = I18n.t('danger.account_not_updated', error_message: I18n.t('danger.user_not_exists'))
    end
    redirect_to account_detail_path(@account)
  end

  private

  def authenticate_status
    if current_user.deactivated
      flash[:danger] = I18n.t('devise.failure.no_permission')
      sign_out(current_user)
      redirect_to new_user_session_path
    end
  end

  def account_exists
    @account = AccountDetail.find_by_id(params[:id])
    if @account.nil?
      flash[:danger] = I18n.t('danger.account_not_exists')
      redirect_to account_details_path
    else
      @account
    end
  end

  def account_details_params
    params.permit(:account_manager_id, :support_executive_id)
  end
end
