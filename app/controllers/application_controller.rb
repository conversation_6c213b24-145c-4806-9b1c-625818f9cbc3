class ApplicationController < ActionController::Base
  include Pagy::Backend

  def append_info_to_payload(payload)
    super
    payload[:level] = 
      case payload[:status]
        when 200
          'INFO'
        when 302 
          'WARN'
        else 
          'ERROR'
      end
    payload[:host] = request.host   
    payload[:remote_ip] = request.remote_ip 
    payload[:ip] = request.ip 
    payload[:user_id] = current_user&.id
  end 
end
