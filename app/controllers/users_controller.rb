# frozen_string_literal: true

# Manage Controller for admin to invite and deactivate support users
class UsersController < ApplicationController
  include Pagy::Backend
  before_action :authenticate_user!, :authenticate_status
  before_action :authenticate_admin, except: %i[index list]
  before_action :user_exists, except: %i[index new create list]

  def index
    @pagy, @users = pagy(User.where(role: USER), items: user_list_params[:items])
  end

  def new
    @user = User.new
  end

  def create
    @user = User.invite!({ **user_params }, current_user)
    if @user.valid?
      flash[:success] = I18n.t('success.invited')
      redirect_to users_path
    else
      flash[:danger] = I18n.t('danger.invalid_data')
      render :new, status: :unprocessable_entity
    end
  end

  def list
    render json: User.where('role = ? and (LOWER(name) LIKE ? or LOWER(email) LIKE ?)', USER, "%#{user_list_params[:key]&.downcase}%", "%#{user_list_params[:key]&.downcase}%")
  end

  def resend_invitation
    user = User.find_by_id(params[:id])
    flash[:success] = I18n.t('success.invited') if user.invite!
    redirect_to users_path
  end

  def edit
    @user = User.find_by_id(params[:id])
  end

  def update
    @user = User.find_by_id(params[:id])
    prev_email = @user.email
    if @user.update(user_params)
      flash[:success] = prev_email == @user.email ? I18n.t('success.name_changed') : I18n.t('success.email_changed')
      @user.invite!
      redirect_to users_path
    else
      flash[:danger] = I18n.t('danger.invalid_data')
      render :edit
    end
  end

  def destroy
    user = User.find_by_id(params[:id])
    if !user.deactivated
      user.update_attribute(:deactivated, true)
      UserMailer.with(admin_email: admin_email, email: user.email).account_deactivated.deliver_now
      flash[:success] = I18n.t('success.user_deactivated')
    end
    redirect_to users_path
  end

  def activate
    user = User.find_by_id(params[:id])
    if user.deactivated
      user.update_attribute(:deactivated, false)
      UserMailer.with(admin_email: admin_email, email: user.email).account_activated.deliver_now
      flash[:success] = I18n.t('success.user_activated')
    end
    redirect_to users_path
  end

  private

  def user_params
    params.require(:user).permit(:name, :email)
  end

  def user_list_params
    params.permit(:page, :key, :items)
  end

  def authenticate_admin
    unless current_user.role == ADMIN
      flash[:danger] = I18n.t('devise.failure.no_permission')
      redirect_to root_path
    end
  end

  def authenticate_status
    if current_user.deactivated
      flash[:danger] = I18n.t('devise.failure.no_permission')
      sign_out(current_user)
      redirect_to new_user_session_path
    end
  end

  def admin_email
    User.find_by(role: ADMIN).email
  end

  def user_exists
    user = User.find_by_id(params[:id])
    if user.nil?
      flash[:danger] = I18n.t('danger.user_not_exists')
      redirect_to users_path and return
    end
  end
end
