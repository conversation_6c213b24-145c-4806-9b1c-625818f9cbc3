class ReportsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_report, only: [:show, :edit, :update, :destroy, :generate, :export, :share]
  before_action :check_report_access, only: [:show, :edit, :update, :destroy, :generate, :export]
  before_action :check_edit_permission, only: [:edit, :update, :destroy]

  def index
    @reports = current_user.reports.includes(:created_by, :latest_result)
    @shared_reports = Report.joins(:report_shares)
                           .where(report_shares: { user: current_user, is_active: true })
                           .includes(:created_by, :latest_result)
    @public_reports = Report.public_reports.includes(:created_by, :latest_result)
    
    # Filter by type if specified
    if params[:type].present?
      @reports = @reports.by_type(params[:type])
      @shared_reports = @shared_reports.by_type(params[:type])
      @public_reports = @public_reports.by_type(params[:type])
    end

    # Filter by data source if specified
    if params[:data_source].present?
      @reports = @reports.by_data_source(params[:data_source])
      @shared_reports = @shared_reports.by_data_source(params[:data_source])
      @public_reports = @public_reports.by_data_source(params[:data_source])
    end

    @pagy_reports, @reports = pagy(@reports, items: 10)
  end

  def show
    @latest_result = @report.latest_result
    @report_filters = @report.report_filters.active.ordered
    @can_edit = can_edit_report?(@report)
    @can_export = can_export_report?(@report)
  end

  def new
    @report = current_user.reports.build
    @available_metrics = get_available_metrics('both')
  end

  def create
    @report = current_user.reports.build(report_params)
    @report.created_by = current_user

    if @report.save
      create_report_filters if params[:filters].present?
      redirect_to @report, notice: 'Report was successfully created.'
    else
      @available_metrics = get_available_metrics(@report.data_source || 'both')
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @available_metrics = get_available_metrics(@report.data_source)
    @report_filters = @report.report_filters.active.ordered
  end

  def update
    if @report.update(report_params)
      update_report_filters if params[:filters].present?
      redirect_to @report, notice: 'Report was successfully updated.'
    else
      @available_metrics = get_available_metrics(@report.data_source)
      @report_filters = @report.report_filters.active.ordered
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @report.destroy
    redirect_to reports_url, notice: 'Report was successfully deleted.'
  end

  def generate
    @report.generate_result!
    redirect_to @report, notice: 'Report generation started. Please refresh the page in a few moments.'
  end

  def export
    @latest_result = @report.latest_result
    
    unless @latest_result&.completed?
      redirect_to @report, alert: 'No completed report data available for export.'
      return
    end

    format = params[:format] || 'csv'
    
    case format
    when 'csv'
      send_data @latest_result.export_to_csv, 
                filename: @latest_result.file_name('csv'),
                type: 'text/csv'
    when 'json'
      send_data @latest_result.export_to_json,
                filename: @latest_result.file_name('json'),
                type: 'application/json'
    else
      redirect_to @report, alert: 'Invalid export format.'
    end
  end

  def share
    # This will be handled by a separate shares controller or modal
    redirect_to @report
  end

  private

  def set_report
    @report = Report.find(params[:id])
  end

  def check_report_access
    unless @report.can_be_accessed_by?(current_user)
      redirect_to reports_path, alert: 'You do not have permission to access this report.'
    end
  end

  def check_edit_permission
    unless can_edit_report?(@report)
      redirect_to @report, alert: 'You do not have permission to edit this report.'
    end
  end

  def can_edit_report?(report)
    permission = report.permission_level_for(current_user)
    %w[edit admin].include?(permission)
  end

  def can_export_report?(report)
    return true if %w[edit admin].include?(report.permission_level_for(current_user))
    
    share = report.report_shares.active.find_by(user: current_user)
    share&.can_export? || false
  end

  def report_params
    params.require(:report).permit(:name, :description, :report_type, :data_source, 
                                  :is_public, :is_scheduled, :schedule_frequency,
                                  metrics: [], chart_config: {})
  end

  def create_report_filters
    params[:filters].each_with_index do |filter_params, index|
      @report.report_filters.create!(
        filter_type: filter_params[:filter_type],
        field_name: filter_params[:field_name],
        operator: filter_params[:operator],
        filter_value: filter_params[:filter_value],
        comparison_config: filter_params[:comparison_config] || {},
        order_index: index
      )
    end
  end

  def update_report_filters
    # Remove existing filters and create new ones
    @report.report_filters.destroy_all
    create_report_filters
  end

  def get_available_metrics(data_source)
    case data_source
    when 'onboarding'
      Report::ONBOARDING_METRICS
    when 'usage'
      Report::USAGE_METRICS
    when 'both'
      Report::ONBOARDING_METRICS + Report::USAGE_METRICS
    else
      []
    end
  end
end
