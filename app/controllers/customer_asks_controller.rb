# frozen_string_literal: true

class CustomerAsksController < ApplicationController
  include Pagy::Backend

  before_action :authenticate_user!, :authenticate_status
  before_action :set_tenant_and_customer_ask, only: %i[show update create]
  before_action :requirement_exists, only: %i[update destroy]

  def show
    @pagy, @customer_requirements = pagy(@customer_ask.customer_requirements, items: params[:items])
  end

  def update
    if @requirement.update(customer_requirement_params)
      flash[:success] = t('customer_asks.customer_requirement.updated')
    else
      flash[:danger] = @requirement.errors.full_messages.to_sentence
    end
    redirect_to customer_asks_account_detail_path
  end

  def create
    new_requirement = CustomerRequirement.new(customer_ask_id: @customer_ask.id, **customer_requirement_params)
    if(new_requirement.save)
      flash[:success] = t('customer_asks.customer_requirement.created')
    else
      flash[:danger] = new_requirement.errors.full_messages.to_sentence
    end
    redirect_to customer_asks_account_detail_path
  end

  def destroy
    if @requirement.destroy
      flash[:success] = t('customer_asks.customer_requirement.destroyed')
    else
      flash[:danger] = @requirement.errors.full_messages.to_sentence
    end
    redirect_to customer_asks_account_detail_path
  end

  private

  def customer_requirement_params
    params.require(:customer_requirement).permit(:id, :category, :status, :due_date, :description)
  end

  def set_tenant_and_customer_ask
    @account_detail = AccountDetail.find_by(id: params[:id])
    unless @account_detail
      redirect_to account_details_path, notice: t('account_details.not_found')
      return
    end
    @tenant_id = @account_detail.tenant_id
    @customer_ask = CustomerAsk.find_or_create_by(tenant_id: @tenant_id)
  end

  def authenticate_status
    if current_user.deactivated
      flash[:danger] = I18n.t('devise.failure.no_permission')
      sign_out(current_user)
      redirect_to new_user_session_path
    end
  end

  def requirement_exists
    @requirement = CustomerRequirement.find_by_id(customer_requirement_params[:id])
    if @requirement.nil?
      flash[:danger] = t('customer_asks.customer_requirement.does_not_exist')
      redirect_to customer_asks_account_detail_path
    end
  end
end
