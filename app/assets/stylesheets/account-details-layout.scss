@import 'variables.scss';

.heading-container{
  display: flex;
  width: 100%;
  height: 10%;
  justify-content: space-between;
  align-items: center;
  .heading{
    display: flex;
    width: 300px;
    font-size: 20px;
    justify-content: start;
    margin-inline: 10px;
    align-items: center;
    flex-direction: row;
    .kylas-icon{
      margin-right: 10px;
      height: 40px;
      aspect-ratio: 1/1;
      color: $text-color;
      border-radius: 100%;
      border: 1px solid $primary;
      background-color: $bg-color-4;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .tenant-id-box{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $bg-color-4;
    border: none;
    border-radius: 4px;
    height: 40px;
    width: fit-content;
    padding: 20px;
    strong {
      margin-right: 5px;
    }
  }
}
.data-container{
  display: flex;
  color: $text-color;
  text-decoration: none;
  flex-direction: column;
  background-color: $nav-bg-color;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  box-shadow: 0px 0px 4px #00000029;
  .tabs{
    display: flex;
    justify-content: start;
    align-items: center;
    border-bottom: 1px solid $dividers;
    .tab{
      text-decoration: none;
      color: $text-color;
      padding: 15px;
      margin-bottom: 3px;
    }
    .active{
      margin-bottom: 0px;
      border-bottom: 3px solid $primary;
    }
    .tab:hover{
      background-color: $bg-color-4;
      cursor: pointer;
    }
  }
  .tab-panel{
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    overflow: auto;
    .account-info{
      display: flex;
      min-height: 200px;
      border-bottom: 1px solid $dividers;
      .basic-info, .contact-info{
        margin-inline: 15px;
        display: flex;
        flex-direction: column;
        width: 50%;
        justify-content: center;
        div{
          display: flex;
          flex-direction: column;
          height: 50%;
          justify-content: space-evenly;
          margin-block: 10px;
        }
      }
    }
    .account-form{
      display: flex;
      flex-direction: column;
      min-height: 250px;
      border-bottom: 1px solid $dividers;
      padding: 15px;
      form{
        display: flex;
        margin-block: 15px;
        flex-direction: column;
        height: 160px;
        justify-content: space-around;
        .select2-selection--single{
          height: 40px !important;
          padding: 6px;
          width: 300px;
        }
        .select2-selection__arrow{
          height: 100%;
          margin-inline: 3px;
        }
      }
    }
    .account-status{
      display: flex;
      min-height: 90px;
      border-bottom: 1px solid $dividers;
      justify-content: space-between;
      align-items: center;
      padding: 0% 2%;
      .row{
        span:nth-child(1){
          color:#687790;
          margin-bottom: 8px;
        }
      }
    }
    .account-actions{
      display: flex;
      min-height: 70px;
      align-items: center;
      justify-content: end;
      input{
        margin-inline: 10px;
        font-weight: 800;
      }
    }
    .marketplace-apps, .usage{
      padding: 15px;
      border-bottom: 1px solid #DFDFDF;
      .add-on-text{
        color: $secondary-text-colour;
        text-align: left;
        font-size: 14px;
        letter-spacing: 0.39px;
        opacity: 1;
        font-weight: 600;
      }
      .word-container {
        display: flex;
        flex-wrap: wrap;
      }
      .word {
        width:33%;
        margin: 8px 0px;
        font-weight: 500;
      }
      div{
        font-size:large;
        font-weight:600;
      }
    }    
  }

  .loader {
    border: 8px solid #DFDFDF;
    border-top: 8px solid $primary;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 2s linear infinite;
    align-self: center;
    justify-self: center;
    position: absolute;
    top: 50%;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.vl{
  border-left:1px solid #DFDFDF;
  height: 80%;
  margin-top: 1%;
}

.email-link, .phone-link {
  text-decoration:none;
  color:black;
  margin-inline:10px;
}

.fa-phone{
  color:green;
}

.fa-envelope{
  color:blue;
}

.basic-info-line{
  margin-inline:5px;
  color:#687790;
}
