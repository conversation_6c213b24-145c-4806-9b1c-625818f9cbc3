@import 'variables';

.rgs-input-container{

  padding: 20px 20px 0px 20px;

  .text-area{
    font-size: 14px;
    color: $caption-subtext-colour;
    height: 20%;
    margin: 4px 0px;
    font-weight: 600;
  }

  .button {
    display: flex;
    justify-content: flex-end;
    position: sticky;
    bottom: 0;
    background-color: $white;
  }

  .btn.save-btn{
    width:80px;
    background-color:$primary;
    color: $btn-text-color;
  }

  .btn.save-btn:hover{
    color: $btn-text-color;
  }
}

.section{

  .for-input-fields {
    margin: 20px 20px;
    font-size: 14px;
    color: $caption-subtext-colour;
  }
}

.onboarding-text{
  font-size: 14px;
  color: $caption-subtext-colour;
  font-weight: bolder;
}

.onboarding-sub-text{
  font-weight: 500;
  color: $caption-subtext-colour;
}

.row {
  position: relative;
}

.v-line {
  position: absolute;
  top: 0px;
  bottom: 0;
  left: 50%;
  border-left: 1px solid $borders-dividers;
}

.form-group {
  align-items: center;
  margin-bottom: 10px;
}

.table{
  margin-top: 20px;

  .table-head {
    color: $secondary-text-colour;
    text-align: left;
    letter-spacing: 0.39px;
    background: #E7F0FD 0% 0% no-repeat padding-box;
    opacity: 1;
  }
}

.for-entity{
  margin-bottom: 20px;
}

.save-btn-rgs {
  background-color: #FFFFFF;
  border: 2px solid $primary;
  color: $primary;
  padding: 1px 6px;
  text-decoration: none;
  display: inline-block;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
}

.save-btn-rgs:hover {
  text-decoration: none;
  color: $primary;
}

.fa-plus{
  margin: 8px 6px;
  color: $primary;
  font-size: 14px;
}

.fa-trash{
  margin: 8px 6px;
  color: $secondary-text-colour;
}

.fa-trash::before {
  font-weight: 900;
  width: 16px;
  height: 18px;
}

.fa-trash:hover {
  color: $error;
}

.table-bordered thead tr th {
  border-bottom-width: 0px !important;
}

input[type=radio] {
  margin: 7px 7px 0 !important;
  margin-top: 1px 9 !important;
  line-height: normal;
}

.expected-volume-container{
  width: 100%;
  display: flex;
  justify-content: space-between;

    .button-group {
    display: flex;
    width: 60%;
    height: 38px;
    padding: 2px 3px;

      .form-control-text {
      margin-right: -2px;
      border-radius: 4px;
      border: solid 0.5px $input-fields-border;
      padding: 9px;
      }
    }
  }

.integrated-button-group{
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.save-cancel-button{

  padding: 15px 0px;

  .save-btn{
    margin-left: 5px;
  }
}

.nested-fields{

  .select2-container--default .select2-selection--single {
    border: 1px solid #aaa;
    border-radius: 4px;
    height: 30px !important;
  }
}

.marketplace-app-select{
  width: 85%;
}

.entity-category-field{
  width: 85%;
}

.unverified-marketplace-app-text-field{
  width: 80%;
}

.cancel-btn{
  height: fit-content;
}
