@import 'variables';

.header-text{
  display: flex;
  margin-inline: 15px;
  justify-content: space-between;
  align-items: center; 
  height: 65px;
  color: $caption-subtext-colour;
  .btn-link{
    text-decoration: none;
  }
}

.requirements-table{
  height: 90%;
  tr{
    margin-inline: 15px;
    button{
      margin-inline: 5px;
    }
  }
}

.pagination{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.csk-btn-grp{
  display: flex;
  .btn-primary, .btn-danger{
    width: 50px;
    height: 50px;
  }
}


.csk-table-container{
  overflow: auto;
  height: 90%;
  .fa-edit{
    color: white;
  }
  .fa-trash{
    color: white;
  }
}

.w-10{
  width: 10%;
}

.w-65{
  width: 65%;
}

.w-5{
  width: 5%;
}

.desc-container{
  word-wrap: break-word;
  max-width: 1000px;
}

.csk-modal-body{
  padding-top: 0px;
}

.csk-modal-close{
  font-size: x-large;
}
