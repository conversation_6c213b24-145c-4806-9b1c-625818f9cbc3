@import 'variables';

.search-bar-container{
  width: 100%;
  height: 5.5%;
  margin-top: 0.5%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .heading{
    color: $secondary-text-color;
    font-size: 26px;
    margin-inline: 10px;
  }
  .search-grp{
    display: flex;
    .search-btn {
      background-color: $nav-bg-color;
      color: $text-color;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 36px;
      height: 36px;
      font-size: 16px;
      margin-inline: 10px;
      border: 1px solid $dividers;
      border-radius: 3px;
      text-decoration: none;
    }
    .input-container{
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-inline: 10px;
      .search-bar{
        background-color: $nav-bg-color;
        color: $text-color;
        width: 150px;
        height: 36px;
        font-size: 16px;
        border: 1px solid $dividers;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        border-right: none;
        outline: none;
        padding-inline: 10px;
      }
      i {
        display: flex;
        align-items: center;
        width: 36px;
        justify-content: center;
        height: 36px;
        background-color: $nav-bg-color;
        border: 1px solid $dividers;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        border-left: none;
      }
    }
  }
}
.list-status{
  color: $active-nav-btn;
  position: relative;
  align-self: flex-end;
  font-size: smaller;
  margin-inline: 10px;
  height: 4%;
  margin: 8px;
}
.data-container{
  width: 100%;
  min-height: 80vh;
  box-shadow: 0px 0px 4px #00000029;
  .data-list{
    display: flex;
    flex-direction: column;
    height: 100%;
    .table-container{
      height: 92%;
      padding: 10px;
      th {
        min-width: 300px;
        height: 60px;
        vertical-align: middle;
        i {
          float: right;
        }
      }
      tr > td > a{
        text-decoration: none;
        color: $text-color;
      }
      tr :hover {
        cursor: pointer;
      }
    }
    .data-footer{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      color: $caption;
      margin-inline: 10px;
      height: 13%;
    }
  }
}
.pagination {
  margin: 0px;
}
.dropdown-menu{
  min-width: 65px;
  max-height: 100px;
  .dropdown-menu-container {
    display: flex;
    min-width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    a{
      text-decoration: none;
      color: $text-color;
      width: 100%;
      text-align: center;
    }
  }
}
