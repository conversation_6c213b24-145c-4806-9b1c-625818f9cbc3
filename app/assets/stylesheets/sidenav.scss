.sidenav {
  background-color: $side-nav-bg-color;
  margin: 0;
  top: 0;
  left: 0;
  width: 15%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .btn-grp {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-top: 7%;
    .btn-name{
      display: inline-block; 
      width: 100%; 
      padding: 7% 0%;
    }
    .nav-btn {
      width: auto;
      margin-left: 3px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      i {
        color: $btn-text-color;
      }
      a {
        letter-spacing: 0.39px;
        text-align: left;
        font: normal normal normal 18px Rubik;
        color: $btn-text-color;
        margin-left: 5px;
        text-decoration: none;
      }
      &:hover {
        background-color: $active-nav-btn;
      }
    }
    .active {
      background-color: $active-nav-btn;
      border-left: 3px solid $nav-bg-color;
      margin-left: 0px;
    }
  }
}
