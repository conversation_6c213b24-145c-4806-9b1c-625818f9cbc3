@import 'variables';

.plan-detail-container{
  border-radius: 4px;
  opacity: 1;
  padding: 20px; 
}

//for 1st section
.plan-detail{

  .horizontal-container {
    display: flex;
  }
  
  .plan-name{
    font-size: 24px;
    color: $secondary-text-colour;
    font-weight: 400;
  }

  .price{
    font-weight: 500;
    font-size: 18px;
    display: inline;
  }

  .month{
    font-size: 13px;
    font-weight: 500;
  }

  .tax{
    font-size: 10px;
    color: $input-fields-border;
  }

  .first-element {
    flex-basis: 40%;
    display: flex;
    align-items: center;
    
      img {
        height: 40%; 
        margin-right: 10px; 
        margin-top: -25px
      }
  
      .text-container {
        flex-direction: column;
      }
    
      h3 {
        margin: 0;
      }
    }

    .other-elements {
      flex-grow: 1;
      display: flex;

      h4{
        font-size: 14px;
      }

      h4.active{
        color: $green;
      }

      h4.inactive{
        color: $red;
      }
    }
    
    .other-elements div {
      flex-grow: 1;
      margin-left: 10px;
    }

    .vr{
      border-left: 1px solid $borders-dividers;
    }
}

.heading-text{
  color: $caption-subtext-colour;
  margin-top: 5px;
  text-align: left;
  font-size: 14px ;
  letter-spacing: 0.39px;
  font-weight: 600;
}

.sub-text{
  font-size: 14px;
  font-weight: 500;
  color: $secondary-text-color;
}

// for 2nd section
.add-on{

   min-height: 37%;

  .add-on-text{
    color: $secondary-text-colour;
    text-align: left;
    font-size: 14px;
    letter-spacing: 0.39px;
    opacity: 1;
    font-weight: 600;
  }
  
  .word-container {
    display: flex;
    flex-wrap: wrap;
  }
  
  .word {
    width: 20%;
    margin: 8px 0px;
  }
  
  }

//third section
.onboarding-date{
  
  height: 40%;

  .onboarding-text {
    color: $secondary-text-colour;
    text-align: left;
    font-size: 14px;
    letter-spacing: 0.39px;
    opacity: 1;
    font-weight: 500;
  }

  .date-field{
    width: 200px !important;
    height: 30px !important;
    border-radius: 4px !important;
    padding: 7px;
    border: 0.5px solid $input-fields-border;
  }
}

//fourth section
.last-updated{
  height: 15%;
  display: flex;
  justify-content: space-between;
  
  .row {
    display: flex;
    align-items: center;
  }

  .updated-text{
    margin-right: 10px;
    font-size: 14px;
    color: $caption-subtext-colour;
  }

  .sub-text{
    font-size: 14px;
    color: $secondary-text-colour;
  }
}

//fifth section
.button {
  display: flex;
  justify-content: flex-end;
  position: sticky;
  bottom: 0;
  background-color: $white;
}

.btn.save-btn{
  width:80px; 
  background-color:$primary; 
  color: $btn-text-color;
}

.btn.save-btn:hover{
  color: $btn-text-color;
}
