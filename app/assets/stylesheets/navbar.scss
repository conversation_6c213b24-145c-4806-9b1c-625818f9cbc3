.navbar {
  background-color: $nav-bg-color;
  width: 100%;
  height: 10%;
  box-shadow: 0px 4px 4px #00000014;
  display: flex;
  flex-direction: row;
  padding: 0px;
  justify-content: space-between;
  .home{
    width: 15%;
    height: 100%;
    background-color: $nav-bg-color;
    justify-self: start;
    display: flex;
    justify-content:left;
    align-items: center;
    padding-left: 30px;
    .home-logo{
      background: url("filled_logo.png") no-repeat;
      background-size: contain;
      height: 70%;
      aspect-ratio: 1/1;
    }
    .home-logo-text {
      margin-left: 5px;
      display: flex;
      flex-direction: column;
      height: 70%;
      line-height: 1.2;
      justify-content: center;
      .brand-name{
        color: $text-color;
        font-weight: bold;
        font-size: larger;
      }
      .sub-brand-name{
        color: $secondary-text-color;
        font-size: x-large;
      }
    }
  }
  .current-user {
    height: 100%;
    justify-self: end;
    aspect-ratio: 1/1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-left: solid 2px $border;
    .dropleft{
      height: 60%;
      width: 60%;
      .current-user-initials{
        background-color: $current-user-bg;
        height: 100%;
        width: 100%;
        border-radius: 100%;
        border: 0px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: larger;
        color: $current-user-initials;
        font-weight: bold;
      }
      a{
        text-decoration: none;
        padding: 20px;
        color: $text-color;
      }
    }
  }
}
