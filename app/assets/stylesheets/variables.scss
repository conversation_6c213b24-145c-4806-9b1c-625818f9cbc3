$white:    #fff !default;
$gray-200: #F9F9F9 !default;
$gray-300: #eef3f5 !default;
$gray-400: #bfc5d2 !default;
$gray-600: #69707f !default;
$gray-800: #343a40 !default;
$gray-900: #2e384d !default;
$light:    $gray-300 !default;
$black:    #000 !default;
$dark-blue: #455067 !default;
$light-blue: #576277 !default;
$scarlet:     #EE2D23 !default;
$scarlet-20: #FBD2CC !default;
$blue:    #007bff !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #dc3545 !default;
$orange:  #fd7e14 !default;
$yellow:  #ffc107 !default;
$green:   #28a745 !default;
$teal:    #20c997 !default;
$cyan:    #17a2b8 !default;
$primary:       $blue !default;
$secondary:     $gray-600 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-800 !default;

$success-green: #28a645;
$dropdown-background-color: $white;
$enable-rounded: true !default;
$border-color: $gray-300 !default;
$input-border-color: $gray-400 !default;
$font-weight-light: 300 !default;
$error: #EE0000;
$input-fields-border: #8B95A7;
$borders-dividers: #DFDFDF;
$secondary-text-colour: #2E384D;
$caption-subtext-colour: #687790;

//breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px
);
$font-light : 300 !default;

$default-padding: 1rem;
$default-margin: 1rem;

$custom-border-width: .0667rem;
$custom-border-light: $custom-border-width solid $border-color;
$custom-border-dark: $custom-border-width solid $input-border-color;

@function valid-radius($radius) {
  $return: ();
  @each $value in $radius {
    @if type-of($value) == number {
      $return: append($return, max($value, 0));
    } @else {
      $return: append($return, $value);
    }
  }
  @return $return;
}

@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {
  @if $enable-rounded {
    border-radius: valid-radius($radius);
  }
  @else if $fallback-border-radius != false {
    border-radius: $fallback-border-radius;
  }
}

$side-nav-bg-color : #455067;
$bg-color : #F9F9F9;
$nav-bg-color : #FFFFFF;
$btn-text-color : #E5F0FE;
$text-color : #30384B;
$secondary-text-color : #2E384D;
$current-user-bg : #E5F0FE;
$current-user-initials : #2C364A;
$active-nav-btn : #576277;
$border : #F9F9F9;
$dividers : #DFDFDF;
$primary : #006DEE;
$caption : #687790;
$bg-color-4 : #E5F0FE;
$customer-requirement-form-bg-color : #EBEDF3

