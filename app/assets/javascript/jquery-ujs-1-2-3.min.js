!function(){"use strict";function t(s,l){var u;s.rails!==l&&s.error("jquery-ujs has already been loaded!");var t=s(document);s.rails=u={linkClickSelector:"a[data-confirm], a[data-method], a[data-remote]:not([disabled]), a[data-disable-with], a[data-disable]",buttonClickSelector:"button[data-remote]:not([form]):not(form button), button[data-confirm]:not([form]):not(form button)",inputChangeSelector:"select[data-remote], input[data-remote], textarea[data-remote]",formSubmitSelector:"form:not([data-turbo=true])",formInputClickSelector:"form:not([data-turbo=true]) input[type=submit], form:not([data-turbo=true]) input[type=image], form:not([data-turbo=true]) button[type=submit], form:not([data-turbo=true]) button:not([type]), input[type=submit][form], input[type=image][form], button[type=submit][form], button[form]:not([type])",disableSelector:"input[data-disable-with]:enabled, button[data-disable-with]:enabled, textarea[data-disable-with]:enabled, input[data-disable]:enabled, button[data-disable]:enabled, textarea[data-disable]:enabled",enableSelector:"input[data-disable-with]:disabled, button[data-disable-with]:disabled, textarea[data-disable-with]:disabled, input[data-disable]:disabled, button[data-disable]:disabled, textarea[data-disable]:disabled",requiredInputSelector:"input[name][required]:not([disabled]), textarea[name][required]:not([disabled])",fileInputSelector:"input[name][type=file]:not([disabled])",linkDisableSelector:"a[data-disable-with], a[data-disable]",buttonDisableSelector:"button[data-remote][data-disable-with], button[data-remote][data-disable]",csrfToken:function(){return s("meta[name=csrf-token]").attr("content")},csrfParam:function(){return s("meta[name=csrf-param]").attr("content")},CSRFProtection:function(t){var e=u.csrfToken();e&&t.setRequestHeader("X-CSRF-Token",e)},refreshCSRFTokens:function(){s('form input[name="'+u.csrfParam()+'"]').val(u.csrfToken())},fire:function(t,e,a){e=s.Event(e);return t.trigger(e,a),!1!==e.result},confirm:function(t){return confirm(t)},ajax:function(t){return s.ajax(t)},href:function(t){return t[0].href},isRemote:function(t){return t.data("remote")!==l&&!1!==t.data("remote")},handleRemote:function(n){if(u.fire(n,"ajax:before")){var t,e,a,o,r=n.data("with-credentials")||null,i=n.data("type")||s.ajaxSettings&&s.ajaxSettings.dataType;return n.is("form")?(t=n.data("ujs:submit-button-formmethod")||n.attr("method"),e=n.data("ujs:submit-button-formaction")||n.attr("action"),a=s(n[0]).serializeArray(),(o=n.data("ujs:submit-button"))&&(a.push(o),n.data("ujs:submit-button",null)),n.data("ujs:submit-button-formmethod",null),n.data("ujs:submit-button-formaction",null)):n.is(u.inputChangeSelector)?(t=n.data("method"),e=n.data("url"),a=n.serialize(),n.data("params")&&(a=a+"&"+n.data("params"))):n.is(u.buttonClickSelector)?(t=n.data("method")||"get",e=n.data("url"),a=n.serialize(),n.data("params")&&(a=a+"&"+n.data("params"))):(t=n.data("method"),e=u.href(n),a=n.data("params")||null),i={type:t||"GET",data:a,dataType:i,beforeSend:function(t,e){if(e.dataType===l&&t.setRequestHeader("accept","*/*;q=0.5, "+e.accepts.script),!u.fire(n,"ajax:beforeSend",[t,e]))return!1;n.trigger("ajax:send",t)},success:function(t,e,a){n.trigger("ajax:success",[t,e,a])},complete:function(t,e){n.trigger("ajax:complete",[t,e])},error:function(t,e,a){n.trigger("ajax:error",[t,e,a])},crossDomain:u.isCrossDomain(e)},r&&(i.xhrFields={withCredentials:r}),e&&(i.url=e),u.ajax(i)}return!1},isCrossDomain:function(t){var e=document.createElement("a");e.href=location.href;var a=document.createElement("a");try{return a.href=t,a.href=a.href,!((!a.protocol||":"===a.protocol)&&!a.host||e.protocol+"//"+e.host==a.protocol+"//"+a.host)}catch(t){return!0}},handleMethod:function(t){var e=u.href(t),a=t.data("method"),n=t.attr("target"),o=u.csrfToken(),r=u.csrfParam(),t=s('<form method="post" action="'+e+'"></form>'),a='<input name="_method" value="'+a+'" type="hidden" />';r===l||o===l||u.isCrossDomain(e)||(a+='<input name="'+r+'" value="'+o+'" type="hidden" />'),n&&t.attr("target",n),t.hide().append(a).appendTo("body"),t.submit()},formElements:function(t,e){return t.is("form")?s(t[0].elements).filter(e):t.find(e)},disableFormElements:function(t){u.formElements(t,u.disableSelector).each(function(){u.disableFormElement(s(this))})},disableFormElement:function(t){var e=t.is("button")?"html":"val",a=t.data("disable-with");a!==l&&(t.data("ujs:enable-with",t[e]()),t[e](a)),t.prop("disabled",!0),t.data("ujs:disabled",!0)},enableFormElements:function(t){u.formElements(t,u.enableSelector).each(function(){u.enableFormElement(s(this))})},enableFormElement:function(t){var e=t.is("button")?"html":"val";t.data("ujs:enable-with")!==l&&(t[e](t.data("ujs:enable-with")),t.removeData("ujs:enable-with")),t.prop("disabled",!1),t.removeData("ujs:disabled")},allowAction:function(t){var e,a=t.data("confirm"),n=!1;if(!a)return!0;if(u.fire(t,"confirm")){try{n=u.confirm(a)}catch(t){(console.error||console.log).call(console,t.stack||t)}e=u.fire(t,"confirm:complete",[n])}return n&&e},blankInputs:function(t,e,a){var n,o,r,i=s(),e=t.find(e||"input,textarea"),l={};return e.each(function(){(n=s(this)).is("input[type=radio]")?(r=n.attr("name"),l[r]||(0===t.find('input[type=radio]:checked[name="'+r+'"]').length&&(o=t.find('input[type=radio][name="'+r+'"]'),i=i.add(o)),l[r]=r)):(n.is("input[type=checkbox],input[type=radio]")?n.is(":checked"):!!n.val())===a&&(i=i.add(n))}),!!i.length&&i},nonBlankInputs:function(t,e){return u.blankInputs(t,e,!0)},stopEverything:function(t){return s(t.target).trigger("ujs:everythingStopped"),t.stopImmediatePropagation(),!1},disableElement:function(t){var e=t.data("disable-with");e!==l&&(t.data("ujs:enable-with",t.html()),t.html(e)),t.on("click.railsDisable",function(t){return u.stopEverything(t)}),t.data("ujs:disabled",!0)},enableElement:function(t){t.data("ujs:enable-with")!==l&&(t.html(t.data("ujs:enable-with")),t.removeData("ujs:enable-with")),t.off("click.railsDisable"),t.removeData("ujs:disabled")}},u.fire(t,"rails:attachBindings")&&(s.ajaxPrefilter(function(t,e,a){t.crossDomain||u.CSRFProtection(a)}),s(window).on("pageshow.rails",function(){s(s.rails.enableSelector).each(function(){var t=s(this);t.data("ujs:disabled")&&s.rails.enableFormElement(t)}),s(s.rails.linkDisableSelector).each(function(){var t=s(this);t.data("ujs:disabled")&&s.rails.enableElement(t)})}),t.on("ajax:complete",u.linkDisableSelector,function(){u.enableElement(s(this))}),t.on("ajax:complete",u.buttonDisableSelector,function(){u.enableFormElement(s(this))}),t.on("click.rails",u.linkClickSelector,function(t){var e=s(this),a=e.data("method"),n=e.data("params"),o=t.metaKey||t.ctrlKey;if(!u.allowAction(e))return u.stopEverything(t);if(!o&&e.is(u.linkDisableSelector)&&u.disableElement(e),u.isRemote(e)){if(o&&(!a||"GET"===a)&&!n)return!0;n=u.handleRemote(e);return!1===n?u.enableElement(e):n.fail(function(){u.enableElement(e)}),!1}return a?(u.handleMethod(e),!1):void 0}),t.on("click.rails",u.buttonClickSelector,function(t){var e=s(this);if(!u.allowAction(e)||!u.isRemote(e))return u.stopEverything(t);e.is(u.buttonDisableSelector)&&u.disableFormElement(e);t=u.handleRemote(e);return!1===t?u.enableFormElement(e):t.fail(function(){u.enableFormElement(e)}),!1}),t.on("change.rails",u.inputChangeSelector,function(t){var e=s(this);return u.allowAction(e)&&u.isRemote(e)?(u.handleRemote(e),!1):u.stopEverything(t)}),t.on("submit.rails",u.formSubmitSelector,function(t){var e,a=s(this),n=u.isRemote(a);if(!u.allowAction(a))return u.stopEverything(t);if(a.attr("novalidate")===l)if(a.data("ujs:formnovalidate-button")===l){if((e=u.blankInputs(a,u.requiredInputSelector,!1))&&u.fire(a,"ajax:aborted:required",[e]))return u.stopEverything(t)}else a.data("ujs:formnovalidate-button",l);if(n){if(n=u.nonBlankInputs(a,u.fileInputSelector)){setTimeout(function(){u.disableFormElements(a)},13);n=u.fire(a,"ajax:aborted:file",[n]);return n||setTimeout(function(){u.enableFormElements(a)},13),n}return u.handleRemote(a),!1}setTimeout(function(){u.disableFormElements(a)},13)}),t.on("click.rails",u.formInputClickSelector,function(t){var e=s(this);if(!u.allowAction(e))return u.stopEverything(t);var a=e.attr("name"),t=a?{name:a,value:e.val()}:null,a=e.closest("form");(a=0===a.length?s("#"+e.attr("form")):a).data("ujs:submit-button",t),a.data("ujs:formnovalidate-button",e.attr("formnovalidate")),a.data("ujs:submit-button-formaction",e.attr("formaction")),a.data("ujs:submit-button-formmethod",e.attr("formmethod"))}),t.on("ajax:send.rails",u.formSubmitSelector,function(t){this===t.target&&u.disableFormElements(s(this))}),t.on("ajax:complete.rails",u.formSubmitSelector,function(t){this===t.target&&u.enableFormElements(s(this))}),s(function(){u.refreshCSRFTokens()}))}window.jQuery?t(jQuery):"object"==typeof exports&&"object"==typeof module&&(module.exports=t)}();