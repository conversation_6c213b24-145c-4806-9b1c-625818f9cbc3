class ReportFilter < ApplicationRecord
  belongs_to :report

  validates :filter_type, presence: true, inclusion: { 
    in: %w[date_range tenant_comparison metric_filter time_period_comparison] 
  }
  validates :field_name, presence: true, unless: -> { filter_type == 'date_range' }
  validates :operator, presence: true, inclusion: { 
    in: %w[equals greater_than less_than between in not_in contains starts_with ends_with] 
  }

  scope :active, -> { where(is_active: true) }
  scope :by_type, ->(type) { where(filter_type: type) }
  scope :ordered, -> { order(:order_index) }

  def apply_filter(query, data_source)
    case filter_type
    when 'date_range'
      apply_date_range_filter(query, data_source)
    when 'tenant_comparison'
      apply_tenant_comparison_filter(query, data_source)
    when 'metric_filter'
      apply_metric_filter(query, data_source)
    when 'time_period_comparison'
      apply_time_period_comparison_filter(query, data_source)
    else
      query
    end
  end

  private

  def apply_date_range_filter(query, data_source)
    return query unless filter_value.present?

    start_date = filter_value['start_date']
    end_date = filter_value['end_date']
    
    return query unless start_date && end_date

    date_field = data_source == 'onboarding' ? 'created_at' : 'date'
    
    case operator
    when 'between'
      query.where("#{date_field} BETWEEN ? AND ?", start_date, end_date)
    when 'greater_than'
      query.where("#{date_field} > ?", start_date)
    when 'less_than'
      query.where("#{date_field} < ?", end_date)
    else
      query
    end
  end

  def apply_tenant_comparison_filter(query, data_source)
    return query unless filter_value.present?

    tenant_ids = filter_value['tenant_ids']
    return query unless tenant_ids.present?

    case operator
    when 'in'
      query.where(tenant_id: tenant_ids)
    when 'not_in'
      query.where.not(tenant_id: tenant_ids)
    else
      query
    end
  end

  def apply_metric_filter(query, data_source)
    return query unless field_name.present? && filter_value.present?

    # Handle JSON field queries for metric_fields
    if field_name.include?('metric_fields.')
      json_field = field_name.split('.').first
      json_key = field_name.split('.')[1..-1].join('.')
      
      case operator
      when 'equals'
        query.where("#{json_field} ->> ? = ?", json_key, filter_value['value'])
      when 'greater_than'
        query.where("(#{json_field} ->> ?)::numeric > ?", json_key, filter_value['value'])
      when 'less_than'
        query.where("(#{json_field} ->> ?)::numeric < ?", json_key, filter_value['value'])
      when 'between'
        query.where("(#{json_field} ->> ?)::numeric BETWEEN ? AND ?", 
                   json_key, filter_value['min'], filter_value['max'])
      else
        query
      end
    else
      # Handle regular field queries
      case operator
      when 'equals'
        query.where(field_name => filter_value['value'])
      when 'greater_than'
        query.where("#{field_name} > ?", filter_value['value'])
      when 'less_than'
        query.where("#{field_name} < ?", filter_value['value'])
      when 'between'
        query.where("#{field_name} BETWEEN ? AND ?", filter_value['min'], filter_value['max'])
      when 'in'
        query.where(field_name => filter_value['values'])
      when 'not_in'
        query.where.not(field_name => filter_value['values'])
      when 'contains'
        query.where("#{field_name} ILIKE ?", "%#{filter_value['value']}%")
      when 'starts_with'
        query.where("#{field_name} ILIKE ?", "#{filter_value['value']}%")
      when 'ends_with'
        query.where("#{field_name} ILIKE ?", "%#{filter_value['value']}")
      else
        query
      end
    end
  end

  def apply_time_period_comparison_filter(query, data_source)
    return query unless comparison_config.present?

    comparison_type = comparison_config['type']
    base_period = comparison_config['base_period']
    
    return query unless comparison_type && base_period

    date_field = data_source == 'onboarding' ? 'created_at' : 'date'
    
    case comparison_type
    when 'current_vs_previous_day'
      current_date = Date.parse(base_period)
      previous_date = current_date - 1.day
      query.where("DATE(#{date_field}) IN (?)", [current_date, previous_date])
    when 'current_vs_previous_week'
      current_week_start = Date.parse(base_period).beginning_of_week
      previous_week_start = current_week_start - 1.week
      query.where("#{date_field} >= ? AND #{date_field} < ?", 
                 previous_week_start, current_week_start + 2.weeks)
    when 'current_vs_previous_month'
      current_month_start = Date.parse(base_period).beginning_of_month
      previous_month_start = current_month_start - 1.month
      query.where("#{date_field} >= ? AND #{date_field} < ?", 
                 previous_month_start, current_month_start + 2.months)
    else
      query
    end
  end
end
