class ReportShare < ApplicationRecord
  belongs_to :report
  belongs_to :user
  belongs_to :shared_by, class_name: 'User'

  validates :permission_level, presence: true, inclusion: { 
    in: %w[view edit admin] 
  }
  validates :user_id, uniqueness: { scope: :report_id }

  scope :active, -> { where(is_active: true) }
  scope :expired, -> { where('expires_at < ?', Time.current) }
  scope :by_permission, ->(level) { where(permission_level: level) }

  before_save :check_expiration

  def expired?
    expires_at.present? && expires_at < Time.current
  end

  def can_view?
    is_active? && !expired?
  end

  def can_edit?
    can_view? && %w[edit admin].include?(permission_level)
  end

  def can_admin?
    can_view? && permission_level == 'admin'
  end

  def can_export?
    can_view? && super
  end

  def can_schedule?
    can_view? && super
  end

  private

  def check_expiration
    if expired?
      self.is_active = false
    end
  end
end
