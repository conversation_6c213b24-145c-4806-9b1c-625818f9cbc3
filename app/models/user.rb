# frozen_string_literal: true

# User Model with two roles admin and user
class User < ApplicationRecord
  after_initialize :set_defaults
  devise :invitable, :database_authenticatable, :recoverable, :rememberable, :validatable
  validates_inclusion_of :role, in: ALLOWED_ROLES
  validates :email, presence: true, uniqueness: true
  validates :name, presence: true

  # Reports associations
  has_many :reports, dependent: :destroy
  has_many :created_reports, class_name: 'Report', foreign_key: 'created_by_id', dependent: :destroy
  has_many :report_shares, dependent: :destroy
  has_many :shared_reports, through: :report_shares, source: :report

  private

  def set_defaults
    self.role = USER if self.role.nil?
    self.deactivated = false if self.deactivated.nil?
  end
end
