class ReportResult < ApplicationRecord
  belongs_to :report

  validates :generated_at, presence: true
  validates :generation_status, presence: true, inclusion: { 
    in: %w[pending processing completed failed] 
  }

  scope :completed, -> { where(generation_status: 'completed') }
  scope :failed, -> { where(generation_status: 'failed') }
  scope :recent, -> { order(generated_at: :desc) }

  def completed?
    generation_status == 'completed'
  end

  def failed?
    generation_status == 'failed'
  end

  def processing?
    generation_status == 'processing'
  end

  def pending?
    generation_status == 'pending'
  end

  def mark_as_processing!
    update!(generation_status: 'processing')
  end

  def mark_as_completed!(data, chart_data = {}, stats = {})
    update!(
      generation_status: 'completed',
      result_data: data,
      chart_data: chart_data,
      summary_stats: stats,
      record_count: data.is_a?(Array) ? data.size : 0,
      error_message: nil
    )
  end

  def mark_as_failed!(error_msg)
    update!(
      generation_status: 'failed',
      error_message: error_msg
    )
  end

  def export_to_csv
    return nil unless completed? && result_data.present?

    CSV.generate(headers: true) do |csv|
      if result_data.is_a?(Array) && result_data.first.is_a?(Hash)
        # Add headers
        csv << result_data.first.keys
        
        # Add data rows
        result_data.each do |row|
          csv << row.values
        end
      end
    end
  end

  def export_to_json
    return nil unless completed?

    {
      report: {
        id: report.id,
        name: report.name,
        type: report.report_type,
        data_source: report.data_source
      },
      generated_at: generated_at,
      record_count: record_count,
      summary_stats: summary_stats,
      data: result_data,
      chart_data: chart_data
    }.to_json
  end

  def file_name(format = 'csv')
    timestamp = generated_at.strftime('%Y%m%d_%H%M%S')
    "#{report.name.parameterize}_#{timestamp}.#{format}"
  end
end
