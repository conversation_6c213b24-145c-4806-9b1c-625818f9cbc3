class Report < ApplicationRecord
  belongs_to :user
  belongs_to :created_by, class_name: 'User'
  has_many :report_filters, dependent: :destroy
  has_many :report_results, dependent: :destroy
  has_many :report_shares, dependent: :destroy
  has_many :shared_users, through: :report_shares, source: :user

  validates :name, presence: true, length: { maximum: 255 }
  validates :report_type, presence: true, inclusion: { in: %w[onboarding usage comparison custom] }
  validates :data_source, presence: true, inclusion: { in: %w[onboarding usage both] }
  validates :schedule_frequency, inclusion: { in: %w[daily weekly monthly quarterly yearly] }, allow_blank: true

  scope :public_reports, -> { where(is_public: true) }
  scope :private_reports, -> { where(is_public: false) }
  scope :scheduled_reports, -> { where(is_scheduled: true) }
  scope :by_type, ->(type) { where(report_type: type) }
  scope :by_data_source, ->(source) { where(data_source: source) }

  # Available metrics for different data sources
  ONBOARDING_METRICS = %w[
    total_users active_users inactive_users admin_users
    total_marketplace_apps active_marketplace_apps
    subscription_plan subscription_status
    account_created_date last_login_date
    storage_used api_calls_made
  ].freeze

  USAGE_METRICS = %w[
    leads_created deals_created contacts_created
    calls_made emails_sent tasks_completed
    login_count active_time_minutes
    features_used integrations_used
    daily_active_users weekly_active_users monthly_active_users
  ].freeze

  COMPARISON_TYPES = %w[
    tenant_vs_tenant
    current_vs_previous_day
    current_vs_previous_week
    current_vs_previous_month
    current_vs_previous_quarter
    current_vs_previous_year
    period_over_period
  ].freeze

  def available_metrics
    case data_source
    when 'onboarding'
      ONBOARDING_METRICS
    when 'usage'
      USAGE_METRICS
    when 'both'
      ONBOARDING_METRICS + USAGE_METRICS
    else
      []
    end
  end

  def can_be_accessed_by?(user)
    return true if self.user == user || created_by == user
    return true if is_public?
    
    report_shares.active.exists?(user: user)
  end

  def permission_level_for(user)
    return 'admin' if self.user == user || created_by == user
    return 'view' if is_public?
    
    share = report_shares.active.find_by(user: user)
    share&.permission_level || 'none'
  end

  def generate_result!
    ReportGenerationJob.perform_later(self)
  end

  def latest_result
    report_results.order(generated_at: :desc).first
  end

  def schedule_next_generation!
    return unless is_scheduled? && schedule_frequency.present?
    
    next_time = case schedule_frequency
                when 'daily'
                  1.day.from_now
                when 'weekly'
                  1.week.from_now
                when 'monthly'
                  1.month.from_now
                when 'quarterly'
                  3.months.from_now
                when 'yearly'
                  1.year.from_now
                end
    
    update!(next_generation_at: next_time)
  end

  def self.due_for_generation
    where(is_scheduled: true)
      .where('next_generation_at <= ?', Time.current)
  end
end
