# frozen_string_literal: true

module AccountDetailsHelper
  include Pagy::Frontend

  def link_to_account_detail(name, account)
    link_to(name.presence || '-', account_detail_path(account), { style: 'display: block' })
  end

  def default_account_manager_name(account)
    if account.account_manager_id.present?
      user = User.find_by_id(account.account_manager_id)
      return "#{user.name} (#{user.email})"
    else
      return nil
    end
  end

  def default_support_executive_name(account)
    if account.support_executive_id.present?
      user = User.find_by_id(account.support_executive_id)
      return "#{user.name} (#{user.email})"
    else
      return nil
    end
  end

  def get_last_updated_by(account)
    name = User.find_by_id(account.last_updated_by_id)&.name
    name.presence || '-'
  end
end
