-- Create development database if it doesn't exist
SELECT 'CREATE DATABASE kylas_customer_success_development'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'kylas_customer_success_development')\gexec

-- Create test database if it doesn't exist
SELECT 'CREATE DATABASE kylas_customer_success_test'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'kylas_customer_success_test')\gexec

-- Create staging database if it doesn't exist
SELECT 'CREATE DATABASE kylas_customer_success_staging'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'kylas_customer_success_staging')\gexec

-- Create production database if it doesn't exist
SELECT 'CREATE DATABASE kylas_customer_success_production'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'kylas_customer_success_production')\gexec
