class CreateReportResults < ActiveRecord::Migration[7.0]
  def change
    create_table :report_results do |t|
      t.references :report, null: false, foreign_key: true
      t.json :result_data, default: {}
      t.json :chart_data, default: {}
      t.json :summary_stats, default: {}
      t.datetime :generated_at, null: false
      t.string :generation_status, default: 'pending' # 'pending', 'processing', 'completed', 'failed'
      t.text :error_message
      t.integer :record_count, default: 0
      t.string :file_path # For exported files
      t.timestamps
    end

    add_index :report_results, :generated_at
    add_index :report_results, :generation_status
  end
end
