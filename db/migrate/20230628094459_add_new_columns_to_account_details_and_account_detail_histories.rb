class AddNewColumnsToAccountDetailsAndAccountDetailHistories < ActiveRecord::Migration[7.0]
  def change
    add_column :account_details, :profile_count, :integer
    add_column :account_details, :account_settings_completed, :boolean
    add_column :account_details, :active_user_count, :integer
    add_column :account_details, :in_active_user_count, :integer
    add_column :account_details, :team_count, :integer
    add_column :account_details, :plan_name, :string
    add_column :account_details, :status, :string
    add_column :account_details, :number_of_custom_dashboards_created, :integer
    add_column :account_details, :message_count, :integer
    add_column :account_details, :number_of_marketplace_apps_installed, :integer
    add_column :account_details, :dau_true, :integer
    add_column :account_details, :dau_false, :integer
    add_column :account_details, :logged_in_users_count, :integer
    add_column :account_details, :active_workflow_count, :integer
    add_column :account_details, :active_custom_fields_count, :integer
    add_column :account_details, :in_active_custom_fields_count, :integer
    add_column :account_details, :product_count, :integer
    add_column :account_details, :import_count, :integer
    add_column :account_details, :pipeline_count, :integer
    add_column :account_details, :lead_count, :integer
    add_column :account_details, :deal_count, :integer
    add_column :account_details, :contact_count, :integer
    add_column :account_details, :company_count, :integer
    add_column :account_details, :call_count, :integer
    add_column :account_details, :meeting_count, :integer
    add_column :account_details, :task_count, :integer
    add_column :account_details, :note_count, :integer
    add_column :account_details, :created_quote_count, :integer
    add_column :account_details, :updated_quote_count, :integer
    add_column :account_details, :active_layout_count, :integer
    add_column :account_details, :inactive_layout_count, :integer
    add_column :account_details, :create_lead_layout_count, :integer
    add_column :account_details, :edit_lead_layout_count, :integer
    add_column :account_details, :goal_trial_active, :string
    add_column :account_details, :goal_addon, :string
    add_column :account_details, :number_of_goals_created, :integer
    add_column :account_details, :subscription_id, :string
    add_column :account_detail_histories, :profile_count, :integer
    add_column :account_detail_histories, :account_settings_completed, :boolean
    add_column :account_detail_histories, :active_user_count, :integer
    add_column :account_detail_histories, :in_active_user_count, :integer
    add_column :account_detail_histories, :team_count, :integer
    add_column :account_detail_histories, :plan_name, :string
    add_column :account_detail_histories, :status, :string
    add_column :account_detail_histories, :number_of_custom_dashboards_created, :integer
    add_column :account_detail_histories, :message_count, :integer
    add_column :account_detail_histories, :number_of_marketplace_apps_installed, :integer
    add_column :account_detail_histories, :dau_true, :integer
    add_column :account_detail_histories, :dau_false, :integer
    add_column :account_detail_histories, :logged_in_users_count, :integer
    add_column :account_detail_histories, :active_workflow_count, :integer
    add_column :account_detail_histories, :active_custom_fields_count, :integer
    add_column :account_detail_histories, :in_active_custom_fields_count, :integer
    add_column :account_detail_histories, :product_count, :integer
    add_column :account_detail_histories, :import_count, :integer
    add_column :account_detail_histories, :pipeline_count, :integer
    add_column :account_detail_histories, :lead_count, :integer
    add_column :account_detail_histories, :deal_count, :integer
    add_column :account_detail_histories, :contact_count, :integer
    add_column :account_detail_histories, :company_count, :integer
    add_column :account_detail_histories, :call_count, :integer
    add_column :account_detail_histories, :meeting_count, :integer
    add_column :account_detail_histories, :task_count, :integer
    add_column :account_detail_histories, :note_count, :integer
    add_column :account_detail_histories, :created_quote_count, :integer
    add_column :account_detail_histories, :updated_quote_count, :integer
    add_column :account_detail_histories, :active_layout_count, :integer
    add_column :account_detail_histories, :inactive_layout_count, :integer
    add_column :account_detail_histories, :create_lead_layout_count, :integer
    add_column :account_detail_histories, :edit_lead_layout_count, :integer
    add_column :account_detail_histories, :goal_trial_active, :string
    add_column :account_detail_histories, :goal_addon, :string
    add_column :account_detail_histories, :number_of_goals_created, :integer
    add_column :account_detail_histories, :subscription_id, :string
  end
end
