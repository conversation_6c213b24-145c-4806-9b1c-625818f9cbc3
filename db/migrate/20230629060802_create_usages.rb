class CreateUsages < ActiveRecord::Migration[7.0]
  def change
    create_table :usages do |t|
      t.bigint :user_id
      t.string :full_name
      t.string :email
      t.string :plan_name
      t.string :status
      t.datetime :last_login_at
      t.datetime :deactivated_at
      t.boolean :active
      t.boolean :verified
      t.integer :created_lead_count
      t.integer :created_deal_count
      t.integer :created_contact_count
      t.integer :updated_lead_count
      t.integer :updated_deal_count
      t.integer :updated_contact_count
      t.integer :created_task_count
      t.integer :created_note_count
      t.integer :created_meeting_count
      t.integer :created_company_count
      t.integer :updated_company_count
      t.boolean :email_account_connected
      t.string :connected_account_name
      t.boolean :logged_in
      t.integer :calls_logged
      t.integer :emails_sent
      t.datetime :date
      t.integer :number_of_custom_dashboards_created
      t.integer :message_count
      t.integer :number_of_marketplace_apps_installed
      t.string :name_of_marketplace_apps_installed, default: [], array: true
      t.boolean :calendar_account_connected
      t.string :connected_calendar_account_name
      t.integer :created_quote_count
      t.integer :updated_quote_count
      t.string :primary_phone_number
      t.string :all_phone_numbers, default: [], array: true
      t.boolean :dau
      t.references :tenant, null: false
      t.timestamps
    end
  end
end
