class CreatePlanDetailHistories < ActiveRecord::Migration[7.0]
  def change
    create_table :plan_detail_histories do |t|
      t.string :name, null: false
      t.datetime :last_paid_on, null: false, precision: 6
      t.datetime :next_renewal, precision: 6
      t.datetime :ob_start, precision: 6
      t.datetime :ob_completion, precision: 6
      t.string :add_on, array: true, default: []
      t.string :status

      t.references :tenant, null: false
      t.timestamps
    end
  end
end
