class AddSampleDataForReporting < ActiveRecord::Migration[7.0]
  def up
    # Create additional users for testing
    create_sample_users

    # Create sample tenants with comprehensive data
    create_sample_tenants

    # Create sample reports with various configurations
    create_sample_reports

    # Create sample report filters
    create_sample_report_filters

    # Create sample report results
    create_sample_report_results

    # Create sample report shares
    create_sample_report_shares
  end

  def down
    # Clean up sample data
    ReportShare.where(report_id: Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ]).pluck(:id)).delete_all

    ReportResult.where(report_id: Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ]).pluck(:id)).delete_all

    ReportFilter.where(report_id: Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ]).pluck(:id)).delete_all

    Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ]).delete_all

    # Clean up sample tenants and related data
    tenant_names = [
      'Acme Corporation', 'TechStart Inc', 'Global Solutions Ltd',
      'Innovation Hub', 'Digital Dynamics', 'Future Systems',
      'Smart Business Co', 'Enterprise Solutions', 'Growth Partners',
      'Success Metrics Inc'
    ]

    tenant_ids = Tenant.where(name: tenant_names).pluck(:id)

    Usage.where(tenant_id: tenant_ids).delete_all
    UsageHistory.where(tenant_id: tenant_ids).delete_all
    AccountDetail.where(tenant_id: tenant_ids).delete_all
    AccountDetailHistory.where(tenant_id: tenant_ids).delete_all
    CustomerRequirement.joins(:customer_ask).where(customer_asks: { tenant_id: tenant_ids }).delete_all
    CustomerAsk.where(tenant_id: tenant_ids).delete_all
    Entity.joins(:rgs_input).where(rgs_inputs: { tenant_id: tenant_ids }).delete_all
    MarketplaceApp.joins(:rgs_input).where(rgs_inputs: { tenant_id: tenant_ids }).delete_all
    RgsInput.where(tenant_id: tenant_ids).delete_all
    Tenant.where(id: tenant_ids).delete_all

    # Clean up sample users (except the main admin)
    User.where(email: [
      '<EMAIL>', '<EMAIL>', '<EMAIL>'
    ]).delete_all
  end

  private

  def create_sample_users
    # Create additional users for testing different roles and permissions
    users_data = [
      {
        name: 'Manager User',
        email: '<EMAIL>',
        password: 'test@123',
        role: 'ADMIN'
      },
      {
        name: 'Data Analyst',
        email: '<EMAIL>',
        password: 'test@123',
        role: 'USER'
      },
      {
        name: 'Support Executive',
        email: '<EMAIL>',
        password: 'test@123',
        role: 'USER'
      }
    ]

    users_data.each do |user_data|
      User.find_or_create_by(email: user_data[:email]) do |user|
        user.name = user_data[:name]
        user.password = user_data[:password]
        user.password_confirmation = user_data[:password]
        user.role = user_data[:role]
      end
    end
  end

  def create_sample_tenants
    # Get users for assignment
    admin_user = User.find_by(email: '<EMAIL>')
    manager_user = User.find_by(email: '<EMAIL>')
    analyst_user = User.find_by(email: '<EMAIL>')
    support_user = User.find_by(email: '<EMAIL>')

    tenants_data = [
      {
        name: 'Acme Corporation',
        kylas_tenant_id: 1001,
        account_detail: {
          email: '<EMAIL>',
          company: 'Acme Corporation',
          mobile: '+**********',
          industry: 'Technology',
          plan_name: 'EXCEED',
          status: 'active',
          active_user_count: 25,
          in_active_user_count: 5,
          lead_count: 150,
          deal_count: 45,
          contact_count: 300,
          account_manager_id: admin_user&.id,
          support_executive_id: support_user&.id
        }
      },
      {
        name: 'TechStart Inc',
        kylas_tenant_id: 1002,
        account_detail: {
          email: '<EMAIL>',
          company: 'TechStart Inc',
          mobile: '+**********',
          industry: 'Software',
          plan_name: 'ELEVATE',
          status: 'active',
          active_user_count: 15,
          in_active_user_count: 2,
          lead_count: 80,
          deal_count: 20,
          contact_count: 150,
          account_manager_id: manager_user&.id,
          support_executive_id: support_user&.id
        }
      },
      {
        name: 'Global Solutions Ltd',
        kylas_tenant_id: 1003,
        account_detail: {
          email: '<EMAIL>',
          company: 'Global Solutions Ltd',
          mobile: '+**********',
          industry: 'Consulting',
          plan_name: 'EXCEED',
          status: 'active',
          active_user_count: 50,
          in_active_user_count: 8,
          lead_count: 300,
          deal_count: 75,
          contact_count: 600,
          account_manager_id: admin_user&.id,
          support_executive_id: analyst_user&.id
        }
      }
    ]

    tenants_data.each do |tenant_data|
      tenant = Tenant.find_or_create_by(kylas_tenant_id: tenant_data[:kylas_tenant_id]) do |t|
        t.name = tenant_data[:name]
        t.system_updated_at = Time.current
      end

      # Create account detail
      account_detail_data = tenant_data[:account_detail]
      account_detail = AccountDetail.find_or_create_by(tenant: tenant) do |ad|
        ad.email = account_detail_data[:email]
        ad.company = account_detail_data[:company]
        ad.mobile = account_detail_data[:mobile]
        ad.industry = account_detail_data[:industry]
        ad.plan_name = account_detail_data[:plan_name]
        ad.status = account_detail_data[:status]
        ad.active_user_count = account_detail_data[:active_user_count]
        ad.in_active_user_count = account_detail_data[:in_active_user_count]
        ad.lead_count = account_detail_data[:lead_count]
        ad.deal_count = account_detail_data[:deal_count]
        ad.contact_count = account_detail_data[:contact_count]
        ad.account_manager_id = account_detail_data[:account_manager_id]
        ad.support_executive_id = account_detail_data[:support_executive_id]
        ad.start_date = 6.months.ago
        ad.last_updated_at = Time.current
        ad.tenant_name = tenant.name
        ad.kylas_tenant_id = tenant.kylas_tenant_id

        # Add comprehensive metric fields
        ad.metric_fields = generate_account_metric_fields(account_detail_data)
      end

      # Create usage data for the last 30 days
      create_usage_data_for_tenant(tenant, account_detail_data[:active_user_count])

      # Create customer asks and requirements
      create_customer_data_for_tenant(tenant, admin_user)

      # Create RGS input data
      create_rgs_data_for_tenant(tenant)
    end
  end

  def generate_account_metric_fields(account_data)
    {
      'profile_count' => account_data[:active_user_count] + account_data[:in_active_user_count],
      'account_settings_completed' => true,
      'team_count' => (account_data[:active_user_count] / 5).ceil,
      'number_of_custom_dashboards_created' => rand(5..15),
      'message_count' => rand(100..500),
      'number_of_marketplace_apps_installed' => rand(3..10),
      'dau_true' => account_data[:active_user_count] * 0.7,
      'dau_false' => account_data[:active_user_count] * 0.3,
      'logged_in_users_count' => account_data[:active_user_count] * 0.8,
      'active_workflow_count' => rand(5..20),
      'active_custom_fields_count' => rand(10..30),
      'in_active_custom_fields_count' => rand(2..8),
      'product_count' => rand(5..25),
      'import_count' => rand(10..50),
      'pipeline_count' => rand(3..8),
      'company_count' => account_data[:contact_count] / 3,
      'call_count' => rand(50..200),
      'meeting_count' => rand(20..80),
      'task_count' => rand(100..300),
      'note_count' => rand(150..400),
      'created_quote_count' => rand(10..50),
      'updated_quote_count' => rand(5..25),
      'active_layout_count' => rand(5..15),
      'inactive_layout_count' => rand(1..5),
      'create_lead_layout_count' => rand(2..5),
      'edit_lead_layout_count' => rand(2..5),
      'goal_trial_active' => ['yes', 'no'].sample,
      'goal_addon' => ['FILE_STORAGE', 'WORKFLOWS', 'EMAIL_TRACKING'].sample,
      'number_of_goals_created' => rand(3..10)
    }
  end

  def create_usage_data_for_tenant(tenant, active_user_count)
    # Create usage data for the last 30 days
    (0..29).each do |days_ago|
      date = days_ago.days.ago.to_date

      # Create multiple usage records per day (simulating different users)
      user_count = [active_user_count, rand(5..active_user_count)].min

      (1..user_count).each do |user_index|
        usage_record = Usage.find_or_create_by(
          tenant: tenant,
          user_id: 1000 + tenant.kylas_tenant_id + user_index,
          date: date
        ) do |usage|
          usage.full_name = "User #{user_index}"
          usage.email = "user#{user_index}@#{tenant.name.downcase.gsub(' ', '')}.com"
          usage.plan_name = tenant.account_detail&.plan_name || 'ELEVATE'
          usage.status = 'active'
          usage.last_login_at = date + rand(8..18).hours
          usage.active = true
          usage.verified = true
          usage.logged_in = [true, false].sample
          usage.dau = usage.logged_in
          usage.tenant_name = tenant.name
          usage.kylas_tenant_id = tenant.kylas_tenant_id

          # Generate realistic usage metrics
          usage.created_lead_count = usage.logged_in ? rand(0..5) : 0
          usage.created_deal_count = usage.logged_in ? rand(0..3) : 0
          usage.created_contact_count = usage.logged_in ? rand(0..8) : 0
          usage.updated_lead_count = usage.logged_in ? rand(0..10) : 0
          usage.updated_deal_count = usage.logged_in ? rand(0..5) : 0
          usage.updated_contact_count = usage.logged_in ? rand(0..15) : 0
          usage.created_task_count = usage.logged_in ? rand(0..3) : 0
          usage.created_note_count = usage.logged_in ? rand(0..5) : 0
          usage.created_meeting_count = usage.logged_in ? rand(0..2) : 0
          usage.created_company_count = usage.logged_in ? rand(0..2) : 0
          usage.updated_company_count = usage.logged_in ? rand(0..3) : 0
          usage.calls_logged = usage.logged_in ? rand(0..8) : 0
          usage.emails_sent = usage.logged_in ? rand(0..12) : 0
          usage.number_of_custom_dashboards_created = usage.logged_in ? rand(0..1) : 0
          usage.message_count = usage.logged_in ? rand(0..10) : 0
          usage.number_of_marketplace_apps_installed = rand(0..2)
          usage.name_of_marketplace_apps_installed = ['Mailchimp', 'Zapier', 'Slack'].sample(usage.number_of_marketplace_apps_installed)
          usage.email_account_connected = [true, false].sample
          usage.connected_account_name = usage.email_account_connected ? 'Gmail' : nil
          usage.calendar_account_connected = [true, false].sample
          usage.connected_calendar_account_name = usage.calendar_account_connected ? 'Google Calendar' : nil
          usage.created_quote_count = usage.logged_in ? rand(0..2) : 0
          usage.updated_quote_count = usage.logged_in ? rand(0..3) : 0
          usage.primary_phone_number = "+1234567#{rand(100..999)}"
          usage.all_phone_numbers = [usage.primary_phone_number]

          # Add comprehensive metric fields
          usage.metric_fields = generate_usage_metric_fields(usage)
        end

        # Also create usage history for some records
        if rand < 0.3 # 30% chance to create history
          UsageHistory.find_or_create_by(
            tenant: tenant,
            user_id: usage_record.user_id,
            date: date
          ) do |history|
            # Copy all attributes from usage_record
            usage_record.attributes.except('id', 'created_at', 'updated_at').each do |key, value|
              history.send("#{key}=", value) if history.respond_to?("#{key}=")
            end
          end
        end
      end
    end
  end

  def generate_usage_metric_fields(usage)
    {
      'session_duration_minutes' => usage.logged_in ? rand(30..240) : 0,
      'pages_visited' => usage.logged_in ? rand(10..50) : 0,
      'features_accessed' => usage.logged_in ? rand(5..15) : 0,
      'api_calls_made' => usage.logged_in ? rand(20..100) : 0,
      'data_exported' => [true, false].sample,
      'mobile_app_used' => [true, false].sample,
      'help_articles_viewed' => usage.logged_in ? rand(0..5) : 0,
      'support_tickets_created' => rand < 0.1 ? 1 : 0,
      'workflow_triggers' => usage.logged_in ? rand(0..10) : 0,
      'custom_field_updates' => usage.logged_in ? rand(0..8) : 0
    }
  end

  def create_customer_data_for_tenant(tenant, admin_user)
    # Create customer ask
    customer_ask = CustomerAsk.find_or_create_by(tenant: tenant) do |ask|
      ask.last_updated_by_id = admin_user&.id
    end

    # Create sample customer requirements
    requirements_data = [
      {
        status: 'IN_PROGRESS',
        category: 'LEAD',
        description: 'Add bulk import functionality for leads with CSV validation',
        due_date: 1.month.from_now
      },
      {
        status: 'COMPLETED',
        category: 'INTEGRATION',
        description: 'Integrate with Mailchimp for email marketing campaigns',
        due_date: 1.week.ago
      },
      {
        status: 'PENDING',
        category: 'CUSTOMISATION',
        description: 'Custom dashboard for sales performance metrics',
        due_date: 2.months.from_now
      },
      {
        status: 'ACCEPTED',
        category: 'AUTOMATION',
        description: 'Automated follow-up emails for new leads',
        due_date: 3.weeks.from_now
      }
    ]

    requirements_data.each do |req_data|
      CustomerRequirement.find_or_create_by(
        customer_ask: customer_ask,
        description: req_data[:description]
      ) do |req|
        req.status = req_data[:status]
        req.category = req_data[:category]
        req.due_date = req_data[:due_date]
      end
    end
  end

  def create_rgs_data_for_tenant(tenant)
    # Create RGS input
    rgs_input = RgsInput.find_or_create_by(tenant: tenant) do |rgs|
      rgs.total_users = tenant.account_detail&.active_user_count || 10
      rgs.total_managers = (rgs.total_users / 5).ceil
    end

    # Create entities
    entities_data = [
      { category: 'LEAD', critical: true, frequency: 'DAILY', expected_volume: 50 },
      { category: 'DEAL', critical: true, frequency: 'DAILY', expected_volume: 20 },
      { category: 'CONTACT', critical: false, frequency: 'DAILY', expected_volume: 30 },
      { category: 'COMPANY', critical: false, frequency: 'DAILY', expected_volume: 10 },
      { category: 'TASK', critical: true, frequency: 'DAILY', expected_volume: 40 }
    ]

    entities_data.each do |entity_data|
      Entity.find_or_create_by(
        rgs_input: rgs_input,
        category: entity_data[:category]
      ) do |entity|
        entity.critical = entity_data[:critical]
        entity.frequency = entity_data[:frequency]
        entity.expected_volume = entity_data[:expected_volume]
      end
    end

    # Create marketplace apps
    apps_data = [
      { name: 'Mailchimp', integrated: true },
      { name: 'Zapier', integrated: true },
      { name: 'Slack', integrated: false },
      { name: 'HubSpot', integrated: false },
      { name: 'Salesforce', integrated: true }
    ]

    apps_data.each do |app_data|
      MarketplaceApp.find_or_create_by(
        rgs_input: rgs_input,
        name: app_data[:name]
      ) do |app|
        app.integrated = app_data[:integrated]
      end
    end
  end

  def create_sample_reports
    admin_user = User.find_by(email: '<EMAIL>')
    manager_user = User.find_by(email: '<EMAIL>')
    analyst_user = User.find_by(email: '<EMAIL>')

    reports_data = [
      {
        name: 'Daily Active Users Report',
        description: 'Track daily active users across all tenants with login patterns and engagement metrics',
        report_type: 'usage',
        data_source: 'usage',
        user: admin_user,
        created_by: admin_user,
        is_public: true,
        is_scheduled: true,
        schedule_frequency: 'daily',
        metrics: {
          'primary_metrics' => ['daily_active_users', 'login_count', 'session_duration'],
          'secondary_metrics' => ['features_used', 'pages_visited'],
          'chart_type' => 'line',
          'group_by' => 'date'
        },
        filters: {},
        chart_config: {
          'chart_type' => 'line',
          'x_axis' => 'date',
          'y_axis' => 'daily_active_users',
          'title' => 'Daily Active Users Trend',
          'show_legend' => true,
          'colors' => ['#007bff', '#28a745', '#ffc107']
        }
      },
      {
        name: 'Monthly Onboarding Analysis',
        description: 'Comprehensive analysis of new customer onboarding patterns and success metrics',
        report_type: 'onboarding',
        data_source: 'onboarding',
        user: manager_user,
        created_by: admin_user,
        is_public: false,
        is_scheduled: true,
        schedule_frequency: 'monthly',
        metrics: {
          'primary_metrics' => ['total_users', 'active_users', 'subscription_plan'],
          'secondary_metrics' => ['account_created_date', 'last_login_date'],
          'chart_type' => 'bar',
          'group_by' => 'month'
        },
        filters: {
          'date_range' => {
            'start_date' => 3.months.ago.to_date,
            'end_date' => Date.current
          }
        },
        chart_config: {
          'chart_type' => 'bar',
          'x_axis' => 'month',
          'y_axis' => 'total_users',
          'title' => 'Monthly Onboarding Trends',
          'show_legend' => true,
          'colors' => ['#17a2b8', '#6f42c1']
        }
      },
      {
        name: 'Tenant Comparison Dashboard',
        description: 'Compare key metrics across different tenants to identify top performers and areas for improvement',
        report_type: 'comparison',
        data_source: 'both',
        user: analyst_user,
        created_by: admin_user,
        is_public: true,
        is_scheduled: false,
        metrics: {
          'primary_metrics' => ['leads_created', 'deals_created', 'contacts_created'],
          'secondary_metrics' => ['calls_made', 'emails_sent', 'tasks_completed'],
          'chart_type' => 'horizontal_bar',
          'group_by' => 'tenant'
        },
        filters: {
          'tenant_comparison' => {
            'comparison_type' => 'tenant_vs_tenant',
            'tenant_ids' => []
          }
        },
        chart_config: {
          'chart_type' => 'horizontal_bar',
          'x_axis' => 'tenant_name',
          'y_axis' => 'total_activities',
          'title' => 'Tenant Performance Comparison',
          'show_legend' => true,
          'colors' => ['#dc3545', '#fd7e14', '#20c997']
        }
      },
      {
        name: 'Usage Trends Analysis',
        description: 'Analyze usage patterns and trends over time to identify peak usage periods and feature adoption',
        report_type: 'usage',
        data_source: 'usage',
        user: admin_user,
        created_by: admin_user,
        is_public: false,
        is_scheduled: true,
        schedule_frequency: 'weekly',
        metrics: {
          'primary_metrics' => ['weekly_active_users', 'features_used', 'integrations_used'],
          'secondary_metrics' => ['api_calls_made', 'data_exported'],
          'chart_type' => 'area',
          'group_by' => 'week'
        },
        filters: {
          'date_range' => {
            'start_date' => 8.weeks.ago.to_date,
            'end_date' => Date.current
          }
        },
        chart_config: {
          'chart_type' => 'area',
          'x_axis' => 'week',
          'y_axis' => 'weekly_active_users',
          'title' => 'Weekly Usage Trends',
          'show_legend' => true,
          'colors' => ['#6610f2', '#e83e8c']
        }
      },
      {
        name: 'Customer Engagement Report',
        description: 'Track customer engagement levels and identify opportunities for increased adoption',
        report_type: 'custom',
        data_source: 'both',
        user: manager_user,
        created_by: manager_user,
        is_public: true,
        is_scheduled: false,
        metrics: {
          'primary_metrics' => ['login_count', 'active_time_minutes', 'features_accessed'],
          'secondary_metrics' => ['help_articles_viewed', 'support_tickets_created'],
          'chart_type' => 'scatter',
          'group_by' => 'tenant'
        },
        filters: {},
        chart_config: {
          'chart_type' => 'scatter',
          'x_axis' => 'login_count',
          'y_axis' => 'active_time_minutes',
          'title' => 'Customer Engagement Matrix',
          'show_legend' => true,
          'colors' => ['#198754', '#0d6efd']
        }
      }
    ]

    reports_data.each do |report_data|
      Report.find_or_create_by(name: report_data[:name]) do |report|
        report.description = report_data[:description]
        report.report_type = report_data[:report_type]
        report.data_source = report_data[:data_source]
        report.user = report_data[:user]
        report.created_by = report_data[:created_by]
        report.is_public = report_data[:is_public]
        report.is_scheduled = report_data[:is_scheduled]
        report.schedule_frequency = report_data[:schedule_frequency]
        report.metrics = report_data[:metrics]
        report.filters = report_data[:filters]
        report.chart_config = report_data[:chart_config]
        report.last_generated_at = rand < 0.7 ? rand(1..7).days.ago : nil
        report.next_generation_at = report.is_scheduled? ? rand(1..24).hours.from_now : nil
      end
    end
  end

  def create_sample_report_filters
    reports = Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ])

    reports.each do |report|
      case report.name
      when 'Daily Active Users Report'
        create_filters_for_dau_report(report)
      when 'Monthly Onboarding Analysis'
        create_filters_for_onboarding_report(report)
      when 'Tenant Comparison Dashboard'
        create_filters_for_comparison_report(report)
      when 'Usage Trends Analysis'
        create_filters_for_trends_report(report)
      when 'Customer Engagement Report'
        create_filters_for_engagement_report(report)
      end
    end
  end

  def create_filters_for_dau_report(report)
    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'date_range',
      order_index: 1
    ) do |filter|
      filter.field_name = 'date'
      filter.operator = 'between'
      filter.filter_value = {
        'start_date' => 30.days.ago.to_date.to_s,
        'end_date' => Date.current.to_s
      }
      filter.is_active = true
    end

    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'metric_filter',
      field_name: 'logged_in',
      order_index: 2
    ) do |filter|
      filter.operator = 'equals'
      filter.filter_value = { 'value' => true }
      filter.is_active = true
    end
  end

  def create_filters_for_onboarding_report(report)
    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'date_range',
      order_index: 1
    ) do |filter|
      filter.field_name = 'created_at'
      filter.operator = 'between'
      filter.filter_value = {
        'start_date' => 3.months.ago.to_date.to_s,
        'end_date' => Date.current.to_s
      }
      filter.is_active = true
    end

    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'metric_filter',
      field_name: 'plan_name',
      order_index: 2
    ) do |filter|
      filter.operator = 'in'
      filter.filter_value = { 'values' => ['ELEVATE', 'EXCEED'] }
      filter.is_active = true
    end
  end

  def create_filters_for_comparison_report(report)
    tenant_ids = Tenant.limit(3).pluck(:id)

    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'tenant_comparison',
      order_index: 1
    ) do |filter|
      filter.field_name = 'tenant_id'
      filter.operator = 'in'
      filter.filter_value = { 'tenant_ids' => tenant_ids }
      filter.comparison_config = {
        'type' => 'tenant_vs_tenant',
        'comparison_period' => 'last_30_days'
      }
      filter.is_active = true
    end
  end

  def create_filters_for_trends_report(report)
    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'time_period_comparison',
      order_index: 1
    ) do |filter|
      filter.field_name = 'date'
      filter.operator = 'between'
      filter.filter_value = {
        'start_date' => 8.weeks.ago.to_date.to_s,
        'end_date' => Date.current.to_s
      }
      filter.comparison_config = {
        'type' => 'current_vs_previous_week',
        'base_period' => Date.current.to_s
      }
      filter.is_active = true
    end
  end

  def create_filters_for_engagement_report(report)
    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'metric_filter',
      field_name: 'metric_fields.session_duration_minutes',
      order_index: 1
    ) do |filter|
      filter.operator = 'greater_than'
      filter.filter_value = { 'value' => 30 }
      filter.is_active = true
    end

    ReportFilter.find_or_create_by(
      report: report,
      filter_type: 'metric_filter',
      field_name: 'active',
      order_index: 2
    ) do |filter|
      filter.operator = 'equals'
      filter.filter_value = { 'value' => true }
      filter.is_active = true
    end
  end

  def create_sample_report_results
    reports = Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ])

    reports.each do |report|
      # Create 2-3 report results for each report to show history
      (1..3).each do |i|
        generated_at = i.days.ago

        ReportResult.find_or_create_by(
          report: report,
          generated_at: generated_at
        ) do |result|
          result.generation_status = 'completed'
          result.result_data = generate_sample_result_data(report)
          result.chart_data = generate_sample_chart_data(report)
          result.summary_stats = generate_sample_summary_stats(report)
          result.record_count = result.result_data.is_a?(Array) ? result.result_data.size : 0
          result.error_message = nil
        end
      end

      # Create one pending result to show the system in action
      ReportResult.find_or_create_by(
        report: report,
        generated_at: 1.hour.from_now
      ) do |result|
        result.generation_status = 'pending'
        result.result_data = {}
        result.chart_data = {}
        result.summary_stats = {}
        result.record_count = 0
        result.error_message = nil
      end
    end
  end

  def generate_sample_result_data(report)
    case report.report_type
    when 'usage'
      generate_usage_result_data
    when 'onboarding'
      generate_onboarding_result_data
    when 'comparison'
      generate_comparison_result_data
    when 'custom'
      generate_custom_result_data
    else
      []
    end
  end

  def generate_usage_result_data
    (0..29).map do |days_ago|
      date = days_ago.days.ago.to_date
      {
        'date' => date.to_s,
        'daily_active_users' => rand(50..150),
        'login_count' => rand(80..200),
        'session_duration_avg' => rand(45..180),
        'features_used' => rand(8..25),
        'pages_visited' => rand(15..60),
        'api_calls' => rand(500..2000)
      }
    end.reverse
  end

  def generate_onboarding_result_data
    (0..11).map do |months_ago|
      date = months_ago.months.ago.beginning_of_month
      {
        'month' => date.strftime('%Y-%m'),
        'new_signups' => rand(10..50),
        'activated_users' => rand(8..40),
        'trial_conversions' => rand(5..25),
        'plan_upgrades' => rand(2..15),
        'churn_rate' => rand(5..20)
      }
    end.reverse
  end

  def generate_comparison_result_data
    Tenant.limit(5).map do |tenant|
      {
        'tenant_id' => tenant.id,
        'tenant_name' => tenant.name,
        'leads_created' => rand(50..300),
        'deals_created' => rand(20..100),
        'contacts_created' => rand(100..500),
        'calls_made' => rand(30..150),
        'emails_sent' => rand(100..400),
        'tasks_completed' => rand(80..250),
        'total_activities' => rand(400..1200)
      }
    end
  end

  def generate_custom_result_data
    Tenant.limit(10).map do |tenant|
      {
        'tenant_id' => tenant.id,
        'tenant_name' => tenant.name,
        'login_count' => rand(20..100),
        'active_time_minutes' => rand(300..1800),
        'features_accessed' => rand(5..20),
        'help_articles_viewed' => rand(0..10),
        'support_tickets_created' => rand(0..3),
        'engagement_score' => rand(60..95)
      }
    end
  end

  def generate_sample_chart_data(report)
    case report.chart_config['chart_type']
    when 'line'
      {
        'labels' => (0..29).map { |i| i.days.ago.strftime('%m/%d') }.reverse,
        'datasets' => [
          {
            'label' => 'Daily Active Users',
            'data' => (0..29).map { rand(50..150) }.reverse,
            'borderColor' => '#007bff',
            'backgroundColor' => 'rgba(0, 123, 255, 0.1)'
          }
        ]
      }
    when 'bar'
      {
        'labels' => (0..11).map { |i| i.months.ago.strftime('%b %Y') }.reverse,
        'datasets' => [
          {
            'label' => 'New Signups',
            'data' => (0..11).map { rand(10..50) }.reverse,
            'backgroundColor' => '#28a745'
          }
        ]
      }
    when 'horizontal_bar'
      tenant_names = Tenant.limit(5).pluck(:name)
      {
        'labels' => tenant_names,
        'datasets' => [
          {
            'label' => 'Total Activities',
            'data' => tenant_names.map { rand(400..1200) },
            'backgroundColor' => ['#dc3545', '#fd7e14', '#20c997', '#6f42c1', '#17a2b8']
          }
        ]
      }
    when 'area'
      {
        'labels' => (0..7).map { |i| i.weeks.ago.strftime('Week %U') }.reverse,
        'datasets' => [
          {
            'label' => 'Weekly Active Users',
            'data' => (0..7).map { rand(200..500) }.reverse,
            'backgroundColor' => 'rgba(102, 16, 242, 0.2)',
            'borderColor' => '#6610f2'
          }
        ]
      }
    when 'scatter'
      {
        'datasets' => [
          {
            'label' => 'Customer Engagement',
            'data' => Tenant.limit(10).map do |tenant|
              {
                'x' => rand(20..100),
                'y' => rand(300..1800),
                'tenant' => tenant.name
              }
            end,
            'backgroundColor' => '#198754'
          }
        ]
      }
    else
      {}
    end
  end

  def generate_sample_summary_stats(report)
    case report.report_type
    when 'usage'
      {
        'total_active_users' => rand(500..1500),
        'avg_session_duration' => rand(60..120),
        'peak_usage_day' => Date.current.strftime('%A'),
        'growth_rate' => "#{rand(5..25)}%",
        'top_feature' => ['Leads', 'Deals', 'Contacts', 'Tasks'].sample
      }
    when 'onboarding'
      {
        'total_signups' => rand(200..800),
        'conversion_rate' => "#{rand(15..35)}%",
        'avg_time_to_activation' => "#{rand(2..7)} days",
        'most_popular_plan' => ['ELEVATE', 'EXCEED'].sample,
        'churn_rate' => "#{rand(5..15)}%"
      }
    when 'comparison'
      {
        'top_performer' => Tenant.order(:name).first&.name || 'Acme Corporation',
        'avg_activities_per_tenant' => rand(300..800),
        'highest_growth' => "#{rand(20..50)}%",
        'total_tenants_analyzed' => Tenant.count,
        'performance_variance' => "#{rand(15..40)}%"
      }
    when 'custom'
      {
        'avg_engagement_score' => rand(70..85),
        'highly_engaged_tenants' => rand(3..8),
        'support_ticket_rate' => "#{rand(2..8)}%",
        'feature_adoption_rate' => "#{rand(60..85)}%",
        'help_usage_trend' => ['Increasing', 'Stable', 'Decreasing'].sample
      }
    else
      {}
    end
  end

  def create_sample_report_shares
    admin_user = User.find_by(email: '<EMAIL>')
    manager_user = User.find_by(email: '<EMAIL>')
    analyst_user = User.find_by(email: '<EMAIL>')
    support_user = User.find_by(email: '<EMAIL>')

    reports = Report.where(name: [
      'Daily Active Users Report',
      'Monthly Onboarding Analysis',
      'Tenant Comparison Dashboard',
      'Usage Trends Analysis',
      'Customer Engagement Report'
    ])

    # Share reports between users with different permission levels
    share_configs = [
      {
        report_name: 'Daily Active Users Report',
        shares: [
          { user: manager_user, shared_by: admin_user, permission_level: 'edit', can_export: true, can_schedule: false },
          { user: analyst_user, shared_by: admin_user, permission_level: 'view', can_export: true, can_schedule: false },
          { user: support_user, shared_by: admin_user, permission_level: 'view', can_export: false, can_schedule: false }
        ]
      },
      {
        report_name: 'Monthly Onboarding Analysis',
        shares: [
          { user: analyst_user, shared_by: admin_user, permission_level: 'edit', can_export: true, can_schedule: true },
          { user: support_user, shared_by: manager_user, permission_level: 'view', can_export: false, can_schedule: false }
        ]
      },
      {
        report_name: 'Usage Trends Analysis',
        shares: [
          { user: manager_user, shared_by: admin_user, permission_level: 'admin', can_export: true, can_schedule: true },
          { user: analyst_user, shared_by: admin_user, permission_level: 'edit', can_export: true, can_schedule: false }
        ]
      },
      {
        report_name: 'Customer Engagement Report',
        shares: [
          { user: admin_user, shared_by: manager_user, permission_level: 'view', can_export: true, can_schedule: false },
          { user: analyst_user, shared_by: manager_user, permission_level: 'edit', can_export: true, can_schedule: false },
          { user: support_user, shared_by: manager_user, permission_level: 'view', can_export: false, can_schedule: false }
        ]
      }
    ]

    share_configs.each do |config|
      report = reports.find_by(name: config[:report_name])
      next unless report

      config[:shares].each do |share_data|
        next unless share_data[:user] && share_data[:shared_by]

        ReportShare.find_or_create_by(
          report: report,
          user: share_data[:user]
        ) do |share|
          share.shared_by = share_data[:shared_by]
          share.permission_level = share_data[:permission_level]
          share.can_export = share_data[:can_export]
          share.can_schedule = share_data[:can_schedule]
          share.expires_at = rand < 0.3 ? 3.months.from_now : nil # 30% chance of expiration
          share.is_active = true
        end
      end
    end
  end
end
