class CreatePlanDetails < ActiveRecord::Migration[7.0]
  def change
    create_table :plan_details do |t|
      t.string :name, null: false
      t.datetime :last_paid_on, null: false, precision: 6
      t.datetime :next_renewal, precision: 6
      t.datetime :ob_start, precision: 6
      t.datetime :ob_completion, precision: 6
      t.string :add_on, array: true, default: []
      t.string :status
      t.bigint :last_updated_by_id, foreign_key: {to_table: :users}
      t.datetime :system_updated_at

      t.references :tenant, null: false

      t.timestamps
    end
  end
end
