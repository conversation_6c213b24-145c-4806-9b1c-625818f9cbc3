class CreateReports < ActiveRecord::Migration[7.0]
  def change
    create_table :reports do |t|
      t.string :name, null: false
      t.text :description
      t.string :report_type, null: false # 'onboarding', 'usage', 'comparison'
      t.string :data_source, null: false # 'onboarding', 'usage', 'both'
      t.json :metrics, default: {}
      t.json :filters, default: {}
      t.json :chart_config, default: {}
      t.references :user, null: false, foreign_key: true
      t.references :created_by, null: false, foreign_key: { to_table: :users }
      t.boolean :is_public, default: false
      t.boolean :is_scheduled, default: false
      t.string :schedule_frequency # 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
      t.datetime :last_generated_at
      t.datetime :next_generation_at
      t.timestamps
    end

    add_index :reports, :name
    add_index :reports, :report_type
    add_index :reports, :data_source
    add_index :reports, :is_public
    add_index :reports, :is_scheduled
  end
end
