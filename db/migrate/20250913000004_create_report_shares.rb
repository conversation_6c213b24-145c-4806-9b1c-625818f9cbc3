class CreateReportShares < ActiveRecord::Migration[7.0]
  def change
    create_table :report_shares do |t|
      t.references :report, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :shared_by, null: false, foreign_key: { to_table: :users }
      t.string :permission_level, default: 'view' # 'view', 'edit', 'admin'
      t.boolean :can_export, default: false
      t.boolean :can_schedule, default: false
      t.datetime :expires_at
      t.boolean :is_active, default: true
      t.timestamps
    end

    add_index :report_shares, [:report_id, :user_id], unique: true
    add_index :report_shares, :permission_level
    add_index :report_shares, :is_active
  end
end
