class CreateReportFilters < ActiveRecord::Migration[7.0]
  def change
    create_table :report_filters do |t|
      t.references :report, null: false, foreign_key: true
      t.string :filter_type, null: false # 'date_range', 'tenant_comparison', 'metric_filter'
      t.string :field_name
      t.string :operator # 'equals', 'greater_than', 'less_than', 'between', 'in', 'not_in'
      t.json :filter_value
      t.json :comparison_config, default: {} # For tenant comparisons, time period comparisons
      t.integer :order_index, default: 0
      t.boolean :is_active, default: true
      t.timestamps
    end

    add_index :report_filters, :filter_type
    add_index :report_filters, :field_name
    add_index :report_filters, :is_active
  end
end
