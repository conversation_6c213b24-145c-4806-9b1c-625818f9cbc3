class CreateAccountDetails < ActiveRecord::Migration[7.0]
  def change
    create_table :account_details do |t|
      t.string :email, null: false
      t.string :company
      t.string :mobile
      t.string :industry
      t.datetime :start_date
      t.datetime :system_updated_at
      t.references :account_manager, foreign_key: { to_table: :users }
      t.references :support_executive, foreign_key: { to_table: :users }
      t.references :last_updated_by, foreign_key: { to_table: :users }
      t.references :tenant, null: false
      t.string :marketplace_apps_installed, default: [], array: true
      t.timestamps
    end
  end
end
