require 'rake'

namespace :data_migration do
  desc 'Migrate tenant data to JSON'
  task :migrate_tenant_data_to_json, [:from_tenant_id, :to_tenant_id] => :environment do |_task, args|
    Rails.logger.info 'Database migration initiated'

    STATIC_USAGE_FIELDS = %i[
      id
      user_id
      full_name
      email
      tenant_id
      tenant_name
      plan_name
      status
      created_at
      updated_at
      last_login_at
      deactivated_at
      active
      verified
      dau
      email_account_connected
      connected_account_name
      logged_in
      date
      name_of_marketplace_apps_installed
      calender_account_connected
      connected_calender_account_name
      primary_phone_number
      all_phone_numbers
      kylas_tenant_id
      metric_fields
    ].freeze

    STATIC_ACCOUNT_DETAILS_FIELDS = %i[
      id
      created_at
      updated_at
      email
      company
      mobile
      industry
      start_date
      account_manager_id
      support_executive_id
      last_updated_by_id
      tenant_id
      marketplace_apps_installed
      account_settings_completed
      plan_name
      status
      dau_true
      dau_false
      subscription_id
      last_updated_at
      kylas_tenant_id
      tenant_name
      metric_fields
    ].freeze

    from_id = args.from_tenant_id.to_i
    to_id   = args.to_tenant_id.to_i

    tenants = if from_id > 0 && to_id > 0
                Tenant.where(id: from_id..to_id)
              else
                Tenant.all
              end

    tenants.find_each(batch_size: 100) do |tenant|
      begin
        if tenant.account_detail.present?
          account_detail_json = tenant.account_detail.as_json(except: STATIC_ACCOUNT_DETAILS_FIELDS)
          tenant.account_detail.update_columns(metric_fields: account_detail_json)
        end

        tenant.account_detail_history.find_each do |history|
          history_json = history.as_json(except: STATIC_ACCOUNT_DETAILS_FIELDS)
          history.update_columns(metric_fields: history_json)
        end

        tenant.usage.find_each do |usage|
          usage_json = usage.as_json(except: STATIC_USAGE_FIELDS)
          usage.update_columns(metric_fields: usage_json)
        end

        tenant.usage_history.find_each do |history|
          history_json = history.as_json(except: STATIC_USAGE_FIELDS)
          history.update_columns(metric_fields: history_json)
        end

        puts "migrate_tenant_data_to_json: Migrated tenant_id #{tenant.id}"
      rescue => e
        puts "migrate_tenant_data_to_json: Exception for tenant_id #{tenant.id} | #{e.message}"
      end
    end

    Rails.logger.info 'Database migration completed'
    puts 'Migration task finished.'
  end

  #   RAILS_ENV=<ENV> bundle exec rake "data_migration:migrate_tenant_data_to_json[1,100]"
  #   RAILS_ENV=<ENV> bundle exec rake "data_migration:migrate_tenant_data_to_json"
end
