GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    aws-eventstream (1.2.0)
    aws-partitions (1.768.0)
    aws-sdk-core (3.173.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.5)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.64.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.122.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.5.2)
      aws-eventstream (~> 1, >= 1.0.2)
    bcrypt (3.1.18)
    bindex (0.8.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    chronic (0.10.2)
    cocoon (1.2.15)
    coderay (1.1.3)
    concurrent-ruby (1.2.2)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    csv (3.3.5)
    date (3.3.3)
    debug (1.7.2)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    devise (4.9.2)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_invitable (2.0.7)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.5.0)
    docile (1.4.0)
    erubi (1.12.0)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (3.2.0)
      i18n (>= 1.8.11, < 2)
    ffi (1.15.5)
    globalid (1.1.0)
      activesupport (>= 5.0)
    hashdiff (1.0.1)
    honeybadger (5.2.1)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    io-console (0.6.0)
    io-wait (0.2.1)
    irb (1.6.4)
      reline (>= 0.3.0)
    jmespath (1.6.2)
    json (2.6.3)
    logdna (1.5.0)
      concurrent-ruby (~> 1.0)
      json (~> 2.0)
      require_all (~> 1.4)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.20.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    method_source (1.0.0)
    mina (1.2.4)
      open4 (~> 1.3.4)
      rake
    mini_mime (1.1.2)
    minitest (5.18.0)
    momentjs-rails (2.29.4.1)
      railties (>= 3.1)
    msgpack (1.7.0)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-smtp (0.3.3)
      net-protocol
    nio4r (2.5.9)
    nokogiri (1.14.3-x86_64-linux)
      racc (~> 1.4)
    open4 (1.3.4)
    orm_adapter (0.5.0)
    pagy (6.0.4)
    pg (1.4.6)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-nav (1.0.0)
      pry (>= 0.9.10, < 0.15)
    public_suffix (4.0.7)
    puma (5.6.5)
      nio4r (~> 2.0)
    racc (1.6.2)
    rack (2.2.6.4)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.0.6)
    reline (0.3.3)
      io-console (~> 0.5)
    request_store (1.5.1)
      rack (>= 1.4)
    require_all (1.5.0)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.5)
    rspec-core (3.12.1)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (6.0.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.11)
      rspec-expectations (~> 3.11)
      rspec-mocks (~> 3.11)
      rspec-support (~> 3.11)
    rspec-support (3.12.0)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    silencer (1.0.1)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sprockets (4.2.0)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    strscan (3.0.1)
    thor (1.2.1)
    tilt (2.1.0)
    timeout (0.3.2)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.18.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    zeitwerk (2.6.7)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3
  bootsnap
  cocoon
  csv
  debug
  devise
  devise_invitable (~> 2.0.0)
  factory_bot_rails
  faker
  honeybadger (~> 5.0)
  io-wait (= 0.2.1)
  logdna (~> 1.5)
  lograge (~> 0.11.2)
  mina
  momentjs-rails
  pagy (~> 6.0)
  pg (~> 1.1)
  pry-nav
  puma (~> 5.0)
  rails (~> 7.0.4, >= *******)
  rails-controller-testing
  rspec-rails (~> 6.0)
  sassc-rails
  silencer (~> 1.0, >= 1.0.1)
  simplecov
  sprockets-rails
  strscan (= 3.0.1)
  tzinfo-data
  web-console
  webmock
  whenever

RUBY VERSION
   ruby 3.1.0p0

BUNDLED WITH
   2.4.10
