# frozen_string_literal: true

require 'mina/rails'
require 'mina/git'
# require 'mina/rbenv'  # for rbenv support. (https://rbenv.org)
require 'mina/rvm' # for rvm support. (https://rvm.io)
require 'mina/default'

# Basic settings:
#   domain       - The hostname to SSH to.
#   deploy_to    - Path to deploy into.
#   repository   - Git repo to clone from. (needed by mina/git)
#   branch       - Branch name to deploy. (needed by mina/git)

set :application_name, 'kylas_customer_success'
set :deploy_to, '/var/www/kylas_customer_success'
set :repository, '**************:amuratech/kylas_customer_success.git'
set :rvm_use_path, '/usr/local/rvm/scripts/rvm'
# Optional settings:
set :user, 'root' # Username in the server to SSH to.
#   set :port, '30000'           # SSH port number.
#set :forward_agent, true     # SSH forward_agent.

env = ENV['on'] || 'staging'
if env == 'production'
  branch = ENV['branch'] || 'main'
  ip = '*************'
  set :shared_files, fetch(:shared_files, []).push('config/credentials/production.key')
else
  branch = ENV['branch'] || 'dev'
  ip = '*************'
  set :bundle_bin, "/usr/local/rvm/rubies/ruby-3.1.0/bin/bundler"
  set :shared_files, fetch(:shared_files, []).push('config/credentials/staging.key')
end

set :domain, ip
set :branch, branch
set :rails_env, env
# Shared dirs and files will be symlinked into the app-folder by the 'deploy:link_shared_paths' step.
# Some plugins already add folders to shared_dirs like `mina/rails` add `public/assets`, `vendor/bundle` and many more
# run `mina -d` to see all folders and files already included in `shared_dirs` and `shared_files`
#set :shared_dirs, fetch(:shared_dirs, []).push('tmp', 'log', 'tmp/cache/bootsnap/')

# This task is the environment that is loaded for all remote run commands, such as
# `mina deploy` or `mina rake`.
task :remote_environment do
  # If you're using rbenv, use this to load the rbenv environment.
  # Be sure to commit your .ruby-version or .rbenv-version to your repository.
  # invoke :'rbenv:load'

  # For those using RVM, use this to load an RVM version@gemset.
  # invoke :'rvm:use', 'ruby-1.9.3-p125@default'
  invoke :'rvm:use', 'ruby-3.1.0'
end

# Put any custom commands you need to run at setup
# All paths in `shared_dirs` and `shared_paths` will be created on their own.
set :shared_dirs, fetch(:shared_dirs, []).push('tmp', 'log')

task :setup do
  command %(touch "#{fetch(:deploy_to)}/shared/log/production.log")
  command %(touch "#{fetch(:deploy_to)}/shared/log/staging.log")
  set :shared_files, fetch(:shared_files, []).push('config/credentials/staging.key')
  set :shared_dirs, fetch(:shared_dirs, []).push('tmp', 'log', 'tmp/cache/bootsnap/')
  set :shared_files, fetch(:shared_files, []).push('log/staging.log')
  set :shared_files, fetch(:shared_files, []).push('log/production.log')
  command %[chmod 0666 "#{fetch(:deploy_to)}/shared/log/production.log"]

  command %[chmod 0666 "#{fetch(:deploy_to)}/shared/log/staging.log"]
  command %(chmod 0777 "#{fetch(:deploy_to)}/shared/tmp")
  command %(mkdir -p "#{fetch(:deploy_to)}/shared/pids/")
  command %(mkdir -p "#{fetch(:deploy_to)}/shared/log/")
  set :shared_files, fetch(:shared_files, []).push('log/staging.log')
  # command %{rbenv install 2.3.0 --skip-existing}
end

desc "Deploys the current version to the server."
task :deploy  => :remote_environment do
  # uncomment this line to make sure you pushed your local branch to the remote origin
  # invoke :'git:ensure_pushed'
  deploy do
    # Put things that will set up an empty directory into a fully set-up
    # instance of your project.
    command %{eval "$(ssh-agent -s)"}
    command %{ssh-add ~/.ssh/id_customer_success}
    invoke :'git:clone'
    invoke :'deploy:link_shared_paths'
    invoke :'bundle:install'
    invoke :'rails:db_migrate'
    invoke :'rails:assets_precompile'
    invoke :'deploy:cleanup'
    on :launch do
      in_path(fetch(:current_path)) do
        command %{mkdir -p tmp/}
        command %{touch tmp/restart.txt}
        # command %{service sidekiq_customer_success restart}
        command %{bundle exec whenever -i kylas_customer_success --update-crontab --set 'environment=#{env}'}
      end
    end
  end

  # you can use `run :local` to run tasks on local machine before of after the deploy scripts
  # run(:local){ say 'done' }
end

# For help in making your deploy script, see the Mina documentation:
#
#  - https://github.com/mina-deploy/mina/tree/master/docs
