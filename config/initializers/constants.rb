# frozen_string_literal: true

PASSWORD_REGEX = /\A(?=.{8,})(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[[:^alnum:]])/x
ALLOWED_ROLES = %w[ADMIN USER]
ALLOWED_PLANS = %w[EMBARK ELEVATE EXCEED]
PLAN_STATUS = %w[ACTIVE INACTIVE]
PLAN_PRICE = {EMBARK: "0", ELEVATE: "12,999", EXCEED: "75,000"}
ENTITY_CATEGORY = %w[LEAD DEAL CONTACT COMPANY CALL_LOG MEETING PRODUCT TASK QUOTATION LAYOUT]
ENTITY_FREQUENCY = %w[DAILY]
ADMIN = 'ADMIN'
USER = 'USER'
MOBILE_NUMBER_REGEX = /\A\+?[1-9][0-9]{7,14}\z/
EMAIL_REGEX = Devise::email_regexp.freeze
ADD_ONS = %w[FILE_STORAGE RECORDS WORKFLOWS EMAIL-TEMPLATES CUSTOM_FIELDS CUSTOM_LAYOUTS EMAIL_TRACKING FILE_STORAGE
             RECORDS WORKFLOWS EMAIL-TEMPLATES CUSTOM_FIELDS CUSTOM_LAYOUTS EMAIL_TRACKING] #will be removed after csv file reading
ACCOUNTS_CONTROLLERS = %w[account_details plan_details rgs_inputs customer_asks].freeze
S3_BUCKET_NAME = Rails.application.credentials.dig(:aws, :bucket_name) || 'dummy-bucket'
S3_ONBOARDING_SHEET_KEY = Rails.env == 'production' ? 'onboarding.json' : 'qa/onboarding.json'
S3_USAGE_SHEET_KEY = Rails.env == 'production' ? 'daily-usage.json' : 'qa/daily-usage.json'
AWS_ACCESS_KEY = Rails.application.credentials.dig(:aws, :access_key_id) || 'dummy-key'
AWS_SECRET_KEY = Rails.application.credentials.dig(:aws, :secret_access_key) || 'dummy-secret'
AWS_REGION = Rails.application.credentials.dig(:aws, :region) || 'us-east-1'
AWS_END_POINT = Rails.application.credentials.dig(:aws, :host) || 'localhost'
CUSTOMER_REQUIREMENT_STATUS = %w[ACCEPTED NOT_DOABLE IN_PROGRESS COMPLETED PENDING]
CUSTOMER_REQUIREMENT_CATEGORY = %w[LEAD DEAL CONTACT COMPANY ACCOUNT CUSTOMISATION INTEGRATION AUTOMATION CALL_LOG MEETING PRODUCT TASK QUOTATION LAYOUT OTHER]
ONBOARDING_SYSTEM_UPDATE_FILE_PATH = 'tmp/onboarding.csv'
USAGE_SYSTEM_UPDATE_FILE_PATH = 'tmp/daily-usage.csv'
KYLAS_APP_NAME = 'customer-success-portal'

if Rails.env.staging? || Rails.env.production?
  LOGDNA = Logdna::Ruby.new(
    Rails.application.credentials.log_dna_ingestion_key,
    { app: "#{KYLAS_APP_NAME.downcase.delete(' ')}-#{Rails.env}", env:Rails.env }
  )
  Rails.logger = LOGDNA
end

CHARGEBEE_SITE = 'kylas'
CHARGEBEE_API_KEY = Rails.application.credentials.dig(:chargebee, :api_key) || 'dummy-key'
CHARGEBEE_URL = Rails.application.credentials.dig(:chargebee, :url) || 'https://dummy.chargebee.com'
USAGE_EXCLUDED_KEYS = %w[created_at id email company mobile industry start_date account_manager_id support_executive_id last_updated_by_id tenant_id marketplace_apps_installed updated_at]

EMBARK = 'Embark'
